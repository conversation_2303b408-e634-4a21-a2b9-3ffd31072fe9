package xiaozhi.modules.agent.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智能体分配实体
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@TableName("ai_agent_assignment")
@Schema(description = "智能体分配信息")
public class AgentAssignmentEntity {

    @TableId(type = IdType.AUTO)
    @Schema(description = "分配ID")
    private Long id;

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "分配状态（0:未分配 1:已分配）")
    private Integer status;

    @Schema(description = "分配时间")
    private Date assignedAt;

    @Schema(description = "取消分配时间")
    private Date unassignedAt;

    @Schema(description = "分配者ID")
    private Long assignedBy;

    @Schema(description = "取消分配者ID")
    private Long unassignedBy;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private Long creator;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新者")
    private Long updater;

    @Schema(description = "更新时间")
    private Date updatedAt;

    @Schema(description = "是否删除（0:未删除 1:已删除）")
    private Integer deleted;
}
