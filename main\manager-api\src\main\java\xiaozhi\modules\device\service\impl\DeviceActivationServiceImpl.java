package xiaozhi.modules.device.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.AllArgsConstructor;
import xiaozhi.common.page.PageData;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.device.dao.DeviceActivationDao;
import xiaozhi.modules.device.dto.DeviceActivationQueryDTO;
import xiaozhi.modules.device.entity.DeviceActivationEntity;
import xiaozhi.modules.device.service.DeviceActivationService;
import xiaozhi.modules.security.tenant.TenantContext;
import xiaozhi.modules.security.user.SecurityUser;

/**
 * 设备激活服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class DeviceActivationServiceImpl extends BaseServiceImpl<DeviceActivationDao, DeviceActivationEntity> implements DeviceActivationService {
    
    private final DeviceActivationDao deviceActivationDao;
    
    @Override
    public PageData<DeviceActivationEntity> pageActivations(DeviceActivationQueryDTO queryDTO) {
        Map<String, Object> params = Map.of(
            "page", queryDTO.getPage(),
            "limit", queryDTO.getLimit()
        );
        
        QueryWrapper<DeviceActivationEntity> wrapper = buildQueryWrapper(queryDTO);
        
        IPage<DeviceActivationEntity> page = baseDao.selectPage(
            getPage(params, "created_at", false),
            wrapper
        );
        
        return new PageData<>(page.getRecords(), page.getTotal());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceActivationEntity createActivationRecord(String macAddress, String agentId, String activationCode) {
        Long currentUserId = SecurityUser.getUserId();
        Long tenantId = TenantContext.getTenantId();
        Date now = new Date();
        
        DeviceActivationEntity activation = new DeviceActivationEntity();
        activation.setMacAddress(macAddress);
        activation.setAgentId(agentId);
        activation.setActivationCode(activationCode);
        activation.setUserId(currentUserId);
        activation.setTenantId(tenantId);
        activation.setActivationStatus(0); // 未激活
        activation.setCodeGeneratedAt(now);
        activation.setCodeExpiredAt(new Date(now.getTime() + 24 * 60 * 60 * 1000)); // 24小时后过期
        activation.setRetryCount(0);
        activation.setCreator(currentUserId);
        activation.setCreatedAt(now);
        activation.setUpdater(currentUserId);
        activation.setUpdatedAt(now);
        activation.setDeleted(0);
        
        baseDao.insert(activation);
        return activation;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActivationStatus(String activationCode, Integer status, String deviceId, String failureReason) {
        Long currentUserId = SecurityUser.getUserId();
        
        deviceActivationDao.updateActivationStatus(activationCode, status, deviceId, failureReason, currentUserId);
    }
    
    @Override
    public DeviceActivationEntity getByActivationCode(String activationCode) {
        return deviceActivationDao.getByActivationCode(activationCode);
    }
    
    @Override
    public List<DeviceActivationEntity> getByMacAddress(String macAddress) {
        return deviceActivationDao.getByMacAddress(macAddress);
    }
    
    @Override
    public List<DeviceActivationEntity> getByDeviceId(String deviceId) {
        return deviceActivationDao.getByDeviceId(deviceId);
    }
    
    @Override
    public boolean isActivationCodeValid(String activationCode) {
        return deviceActivationDao.isActivationCodeValid(activationCode);
    }
    
    @Override
    public int getUnactivatedDeviceCount(Long userId) {
        return deviceActivationDao.getUnactivatedDeviceCount(userId);
    }
    
    @Override
    public List<Object> getTenantActivationStatistics(Long tenantId, String startDate, String endDate) {
        return deviceActivationDao.getTenantActivationStatistics(tenantId, startDate, endDate);
    }
    
    /**
     * 构建查询条件
     */
    private QueryWrapper<DeviceActivationEntity> buildQueryWrapper(DeviceActivationQueryDTO queryDTO) {
        QueryWrapper<DeviceActivationEntity> wrapper = new QueryWrapper<>();
        
        // 设备ID
        if (StringUtils.isNotBlank(queryDTO.getDeviceId())) {
            wrapper.eq("device_id", queryDTO.getDeviceId());
        }
        
        // MAC地址
        if (StringUtils.isNotBlank(queryDTO.getMacAddress())) {
            wrapper.eq("mac_address", queryDTO.getMacAddress());
        }
        
        // 激活码
        if (StringUtils.isNotBlank(queryDTO.getActivationCode())) {
            wrapper.eq("activation_code", queryDTO.getActivationCode());
        }
        
        // 智能体ID
        if (StringUtils.isNotBlank(queryDTO.getAgentId())) {
            wrapper.eq("agent_id", queryDTO.getAgentId());
        }
        
        // 用户ID
        if (queryDTO.getUserId() != null) {
            wrapper.eq("user_id", queryDTO.getUserId());
        }
        
        // 激活状态
        if (queryDTO.getActivationStatus() != null) {
            wrapper.eq("activation_status", queryDTO.getActivationStatus());
        }
        
        // 激活时间范围
        if (StringUtils.isNotBlank(queryDTO.getActivatedStartDate())) {
            wrapper.ge("activated_at", queryDTO.getActivatedStartDate());
        }
        if (StringUtils.isNotBlank(queryDTO.getActivatedEndDate())) {
            wrapper.le("activated_at", queryDTO.getActivatedEndDate());
        }
        
        // 设备硬件型号
        if (StringUtils.isNotBlank(queryDTO.getBoard())) {
            wrapper.eq("board", queryDTO.getBoard());
        }
        
        // 排除已删除的记录
        wrapper.eq("deleted", 0);
        
        // 默认按创建时间倒序排列
        wrapper.orderByDesc("created_at");
        
        return wrapper;
    }
}
