package xiaozhi.modules.sys.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import xiaozhi.common.dao.BaseDao;
import xiaozhi.modules.sys.entity.SysOrganizationEntity;

/**
 * 企业组织DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Mapper
public interface SysOrganizationDao extends BaseDao<SysOrganizationEntity> {
    
    /**
     * 根据企业编码获取企业信息
     * 
     * @param orgCode 企业编码
     * @return 企业实体
     */
    SysOrganizationEntity getByOrgCode(@Param("orgCode") String orgCode);
    
    /**
     * 根据企业类型获取企业列表
     * 
     * @param orgType 企业类型
     * @return 企业实体列表
     */
    List<SysOrganizationEntity> getOrganizationsByType(@Param("orgType") Integer orgType);
    
    /**
     * 获取即将到期的企业列表
     * 
     * @param days 天数
     * @return 企业实体列表
     */
    List<SysOrganizationEntity> getExpiringOrganizations(@Param("days") Integer days);
}
