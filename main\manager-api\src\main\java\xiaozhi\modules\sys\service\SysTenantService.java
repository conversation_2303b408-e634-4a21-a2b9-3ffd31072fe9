package xiaozhi.modules.sys.service;

import java.util.List;
import java.util.Map;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.sys.dto.TenantCreateRequestDTO;
import xiaozhi.modules.sys.dto.TenantDetailResponseDTO;
import xiaozhi.modules.sys.dto.TenantQueryRequestDTO;
import xiaozhi.modules.sys.dto.TenantUpdateRequestDTO;
import xiaozhi.modules.sys.entity.SysTenantEntity;

/**
 * 租户Service
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface SysTenantService extends BaseService<SysTenantEntity> {
    
    /**
     * 根据租户编码获取租户信息
     * 
     * @param tenantCode 租户编码
     * @return 租户实体
     */
    SysTenantEntity getByTenantCode(String tenantCode);
    
    /**
     * 根据企业ID获取租户列表
     * 
     * @param orgId 企业ID
     * @return 租户实体列表
     */
    List<SysTenantEntity> getTenantsByOrgId(Long orgId);
    
    /**
     * 获取即将到期的租户列表
     *
     * @param days 天数
     * @return 租户实体列表
     */
    List<SysTenantEntity> getExpiringTenants(Integer days);

    /**
     * 分页查询租户详情
     *
     * @param queryRequest 查询请求
     * @return 分页数据
     */
    PageData<TenantDetailResponseDTO> pageWithDetails(TenantQueryRequestDTO queryRequest);

    /**
     * 根据ID获取租户详情
     *
     * @param tenantId 租户ID
     * @return 租户详情
     */
    TenantDetailResponseDTO getTenantDetail(Long tenantId);

    /**
     * 创建租户
     *
     * @param createRequest 创建请求
     */
    void createTenant(TenantCreateRequestDTO createRequest);

    /**
     * 更新租户
     *
     * @param updateRequest 更新请求
     */
    void updateTenant(TenantUpdateRequestDTO updateRequest);

    /**
     * 删除租户
     *
     * @param tenantId 租户ID
     */
    void deleteTenant(Long tenantId);

    /**
     * 更新租户状态
     *
     * @param tenantId 租户ID
     * @param status 状态
     */
    void updateTenantStatus(Long tenantId, Integer status);

    /**
     * 检查租户编码是否存在
     *
     * @param tenantCode 租户编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsTenantCode(String tenantCode, Long excludeId);

    /**
     * 获取租户统计信息
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    Map<String, Object> getTenantStatistics(Long tenantId);
}
