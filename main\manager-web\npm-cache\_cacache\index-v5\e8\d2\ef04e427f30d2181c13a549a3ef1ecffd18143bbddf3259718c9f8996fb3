
391a50ab1bc0b7a3664586c0a3d9405f0d87c70c	{"key":"make-fetch-happen:request-cache:https://cdn.npmmirror.com/packages/%40babel/plugin-transform-dotall-regex/7.25.9/plugin-transform-dotall-regex-7.25.9.tgz","integrity":"sha512-t7ZQ7g5trIgSRYhI9pIJtRl64KHotutUJsh4Eze5l7olJv+mRSg4/MmbZ0tv1eeqRbdvo/+trvJD/Oc5DmW2cA==","time":1748942024809,"size":2086,"metadata":{"time":1748942023142,"url":"https://cdn.npmmirror.com/packages/%40babel/plugin-transform-dotall-regex/7.25.9/plugin-transform-dotall-regex-7.25.9.tgz","reqHeaders":{},"resHeaders":{"cache-control":"max-age=86400","content-type":"application/octet-stream","date":"Mon, 02 Jun 2025 09:21:00 GMT","etag":"\"44219D50DF3D4530ED75B86A881C9DAA\"","last-modified":"Tue, 22 Oct 2024 15:34:09 GMT"},"options":{"compress":true}}}