# 权限设计优化说明

## 1. 优化背景

根据您的反馈，原有的权限设计存在以下问题：

1. **前后端耦合**：权限表中区分菜单、按钮、数据3种权限类型，将前后端耦合在一起
2. **不符合最佳实践**：权限应该只和具体的操作有关，而与某个具体的页面元素没有关系
3. **未遵循Shiro规范**：现有权限是通过Shiro框架进行控制的，应该遵从Shiro最佳实践

## 2. 优化方案

### 2.1 权限设计原则

#### 2.1.1 解耦原则
- **权限与UI解耦**：权限只关注业务操作，不关注前端展示
- **操作导向**：权限基于具体的业务操作定义
- **统一格式**：采用Shiro标准的`domain:action:instance`格式

#### 2.1.2 Shiro最佳实践
- **通配符支持**：支持`*`通配符，实现灵活的权限控制
- **层次化设计**：权限具有明确的层次结构
- **实例级控制**：支持到具体资源实例的权限控制

### 2.2 权限字符串格式

```
domain:action:instance
```

- **domain**：权限域（资源类型），如：organization、tenant、user、agent、device
- **action**：操作类型，如：view、create、update、delete、bind、unbind
- **instance**：实例标识，如：*（所有）、tenant（租户范围）、具体ID

### 2.3 权限示例对比

#### 2.3.1 优化前（错误设计）
```
权限类型：菜单、按钮、数据
权限编码：ORG_VIEW、ORG_CREATE、DEVICE_BIND_BUTTON
```

#### 2.3.2 优化后（Shiro格式）
```
organization:view:*          # 查看所有企业
organization:create:*        # 创建企业
user:view:tenant            # 查看租户内用户
device:bind:tenant          # 绑定租户设备
device:view:12345           # 查看特定设备
```

## 3. 数据库设计优化

### 3.1 权限表重新设计

#### 3.1.1 优化前
```sql
CREATE TABLE `sys_permission` (
    `permission_code` VARCHAR(100) NOT NULL,
    `permission_name` VARCHAR(100) NOT NULL,
    `permission_type` TINYINT DEFAULT 1,  -- 问题：类型耦合
    `parent_id` BIGINT DEFAULT 0,         -- 问题：树形结构复杂
    `path` VARCHAR(200),                  -- 问题：前端路径耦合
    `component` VARCHAR(200),             -- 问题：前端组件耦合
    `icon` VARCHAR(50)                    -- 问题：UI元素耦合
);
```

#### 3.1.2 优化后
```sql
CREATE TABLE `sys_permission` (
    `permission` VARCHAR(200) NOT NULL,   -- Shiro格式权限字符串
    `description` VARCHAR(200) NOT NULL,  -- 权限描述
    `domain` VARCHAR(50) NOT NULL,        -- 权限域
    `action` VARCHAR(50) NOT NULL,        -- 操作类型
    `instance` VARCHAR(100)               -- 实例标识（可选）
);

-- 新增菜单表，与权限解耦
CREATE TABLE `sys_menu` (
    `menu_code` VARCHAR(50) NOT NULL,
    `menu_name` VARCHAR(100) NOT NULL,
    `required_permission` VARCHAR(200),   -- 关联权限字符串
    `path` VARCHAR(200),                  -- 前端路径
    `component` VARCHAR(200),             -- 前端组件
    `icon` VARCHAR(50)                    -- 图标
);
```

### 3.2 设计优势

#### 3.2.1 解耦优势
- **权限表**：只关注业务权限，不包含UI信息
- **菜单表**：只关注前端展示，通过`required_permission`字段关联权限
- **灵活性**：菜单和权限可以独立变更

#### 3.2.2 扩展性优势
- **新增权限**：只需在权限表中添加新的权限字符串
- **新增菜单**：只需在菜单表中添加新的菜单项
- **权限变更**：不影响菜单结构

## 4. 代码实现优化

### 4.1 后端权限校验

#### 4.1.1 注解式校验
```java
@RestController
public class DeviceController {
    
    @GetMapping("/list")
    @RequiresPermissions("device:view:tenant")  // Shiro标准注解
    public Result<List<Device>> getDeviceList() {
        return Result.ok(deviceService.getDeviceList());
    }
    
    @PostMapping("/bind")
    @RequiresPermissions("device:bind:tenant")
    public Result<Void> bindDevice(@RequestBody DeviceBindDTO dto) {
        deviceService.bindDevice(dto);
        return Result.ok();
    }
}
```

#### 4.1.2 编程式校验
```java
@Service
public class DeviceService {
    
    public List<Device> getDeviceList() {
        Subject subject = SecurityUtils.getSubject();
        
        if (subject.isPermitted("device:view:*")) {
            // 平台管理员，查看所有设备
            return deviceMapper.selectAll();
        } else if (subject.isPermitted("device:view:tenant")) {
            // 租户用户，只查看租户设备
            return deviceMapper.selectByTenantId(getCurrentTenantId());
        } else {
            throw new UnauthorizedException("无权限访问设备信息");
        }
    }
}
```

### 4.2 前端权限控制

#### 4.2.1 菜单权限控制
```javascript
// 根据权限过滤菜单
function filterMenuByPermission(menus, permissions) {
    return menus.filter(menu => {
        if (menu.meta && menu.meta.permission) {
            return permissions.includes(menu.meta.permission) || 
                   permissions.includes('*:*:*');
        }
        return true;
    });
}
```

#### 4.2.2 按钮权限控制
```vue
<template>
  <div>
    <!-- 使用权限字符串控制按钮显示 -->
    <el-button 
      v-if="hasPermission('device:create:tenant')"
      @click="createDevice">
      新增设备
    </el-button>
    
    <el-button 
      v-if="hasPermission('device:delete:tenant')"
      @click="deleteDevice">
      删除设备
    </el-button>
  </div>
</template>
```

## 5. 权限配置示例

### 5.1 角色权限配置

#### 5.1.1 平台管理员
```sql
-- 拥有所有权限（通配符）
INSERT INTO sys_role_permission (role_id, permission_id) 
SELECT 1, id FROM sys_permission WHERE permission LIKE '%:*';
```

#### 5.1.2 租户管理员
```sql
-- 租户范围内的管理权限
INSERT INTO sys_role_permission (role_id, permission_id) 
SELECT 2, id FROM sys_permission WHERE permission LIKE '%:tenant';
```

#### 5.1.3 普通用户
```sql
-- 只有查看权限
INSERT INTO sys_role_permission (role_id, permission_id) 
SELECT 3, id FROM sys_permission WHERE permission LIKE '%:view:tenant';
```

### 5.2 动态权限示例

```java
// 检查用户是否可以操作特定设备
public boolean canOperateDevice(String deviceId, String action) {
    Subject subject = SecurityUtils.getSubject();
    
    // 检查通配符权限
    if (subject.isPermitted("device:" + action + ":*")) {
        return true;
    }
    
    // 检查租户权限
    if (subject.isPermitted("device:" + action + ":tenant")) {
        // 验证设备是否属于当前租户
        return deviceService.belongsToCurrentTenant(deviceId);
    }
    
    // 检查具体设备权限
    return subject.isPermitted("device:" + action + ":" + deviceId);
}
```

## 6. 迁移指南

### 6.1 数据迁移

#### 6.1.1 权限数据转换
```sql
-- 将旧的权限编码转换为Shiro格式
UPDATE sys_permission SET 
    permission = CASE 
        WHEN permission_code = 'ORG_VIEW' THEN 'organization:view:*'
        WHEN permission_code = 'ORG_CREATE' THEN 'organization:create:*'
        WHEN permission_code = 'DEVICE_BIND' THEN 'device:bind:tenant'
        -- ... 其他转换规则
    END;
```

#### 6.1.2 菜单数据迁移
```sql
-- 创建菜单表并迁移数据
INSERT INTO sys_menu (menu_code, menu_name, required_permission, path, component, icon)
SELECT permission_code, permission_name, permission, path, component, icon
FROM sys_permission 
WHERE permission_type = 1; -- 原菜单类型
```

### 6.2 代码迁移

#### 6.2.1 注解替换
```java
// 替换前
@RequiresPermissions("ORG_VIEW")

// 替换后
@RequiresPermissions("organization:view:*")
```

#### 6.2.2 前端权限检查
```javascript
// 替换前
hasPermission('ORG_VIEW')

// 替换后
hasPermission('organization:view:*')
```

## 7. 总结

### 7.1 优化效果

1. **解耦成功**：权限与UI完全分离，各自独立演进
2. **符合规范**：遵循Shiro最佳实践，权限字符串标准化
3. **灵活性强**：支持通配符、层次化、实例级权限控制
4. **易于维护**：权限逻辑清晰，扩展简单

### 7.2 最佳实践

1. **权限设计**：基于业务操作，不关注UI展示
2. **命名规范**：使用`domain:action:instance`格式
3. **层次控制**：从粗粒度到细粒度的权限控制
4. **性能优化**：合理使用缓存，避免频繁权限查询

这次优化完全解决了前后端耦合问题，建立了符合Shiro最佳实践的权限体系，为系统的长期发展奠定了坚实基础。
