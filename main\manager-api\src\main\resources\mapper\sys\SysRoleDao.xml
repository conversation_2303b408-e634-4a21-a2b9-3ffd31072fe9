<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xiaozhi.modules.sys.dao.SysRoleDao">

    <!-- 根据用户ID获取角色列表 -->
    <select id="getUserRoles" resultType="java.lang.String">
        SELECT DISTINCT r.role_code
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.status = 1
          AND r.deleted = 0
          AND ur.deleted = 0
    </select>

    <!-- 根据租户ID获取角色列表 -->
    <select id="getRolesByTenantId" resultType="xiaozhi.modules.sys.entity.SysRoleEntity">
        SELECT *
        FROM sys_role
        WHERE (tenant_id = #{tenantId} OR tenant_id IS NULL)
          AND status = 1
          AND deleted = 0
        ORDER BY role_type, role_code
    </select>

    <!-- 根据角色类型获取角色列表 -->
    <select id="getRolesByType" resultType="xiaozhi.modules.sys.entity.SysRoleEntity">
        SELECT *
        FROM sys_role
        WHERE role_type = #{roleType}
          AND status = 1
          AND deleted = 0
        ORDER BY role_code
    </select>

</mapper>
