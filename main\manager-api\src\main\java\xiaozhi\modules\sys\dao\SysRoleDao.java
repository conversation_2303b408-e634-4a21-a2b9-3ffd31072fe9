package xiaozhi.modules.sys.dao;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import xiaozhi.common.dao.BaseDao;
import xiaozhi.modules.sys.entity.SysRoleEntity;

/**
 * 角色DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Mapper
public interface SysRoleDao extends BaseDao<SysRoleEntity> {
    
    /**
     * 根据用户ID获取角色列表
     * 
     * @param userId 用户ID
     * @return 角色编码集合
     */
    Set<String> getUserRoles(@Param("userId") Long userId);
    
    /**
     * 根据租户ID获取角色列表
     * 
     * @param tenantId 租户ID
     * @return 角色实体列表
     */
    List<SysRoleEntity> getRolesByTenantId(@Param("tenantId") Long tenantId);
    
    /**
     * 根据角色类型获取角色列表
     * 
     * @param roleType 角色类型
     * @return 角色实体列表
     */
    List<SysRoleEntity> getRolesByType(@Param("roleType") Integer roleType);
}
