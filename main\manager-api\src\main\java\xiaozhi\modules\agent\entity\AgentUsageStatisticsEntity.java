package xiaozhi.modules.agent.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智能体使用统计实体
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@TableName("ai_agent_usage_statistics")
@Schema(description = "智能体使用统计信息")
public class AgentUsageStatisticsEntity {

    @TableId(type = IdType.AUTO)
    @Schema(description = "统计ID")
    private Long id;

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "统计日期")
    private Date statisticsDate;

    @Schema(description = "使用次数")
    private Integer usageCount;

    @Schema(description = "总使用时长（秒）")
    private Long totalDuration;

    @Schema(description = "平均使用时长（秒）")
    private Long avgDuration;

    @Schema(description = "最长使用时长（秒）")
    private Long maxDuration;

    @Schema(description = "最短使用时长（秒）")
    private Long minDuration;

    @Schema(description = "成功次数")
    private Integer successCount;

    @Schema(description = "失败次数")
    private Integer failureCount;

    @Schema(description = "错误率")
    private Double errorRate;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;
}
