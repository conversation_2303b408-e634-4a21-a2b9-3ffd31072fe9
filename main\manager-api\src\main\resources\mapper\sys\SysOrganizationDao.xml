<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xiaozhi.modules.sys.dao.SysOrganizationDao">

    <!-- 根据企业编码获取企业信息 -->
    <select id="getByOrgCode" resultType="xiaozhi.modules.sys.entity.SysOrganizationEntity">
        SELECT *
        FROM sys_organization
        WHERE org_code = #{orgCode}
          AND deleted = 0
    </select>

    <!-- 根据企业类型获取企业列表 -->
    <select id="getOrganizationsByType" resultType="xiaozhi.modules.sys.entity.SysOrganizationEntity">
        SELECT *
        FROM sys_organization
        WHERE org_type = #{orgType}
          AND deleted = 0
        ORDER BY create_date DESC
    </select>

    <!-- 获取即将到期的企业列表 -->
    <select id="getExpiringOrganizations" resultType="xiaozhi.modules.sys.entity.SysOrganizationEntity">
        SELECT *
        FROM sys_organization
        WHERE expire_date IS NOT NULL
          AND expire_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY)
          AND status = 1
          AND deleted = 0
        ORDER BY expire_date ASC
    </select>

</mapper>
