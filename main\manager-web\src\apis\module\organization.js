import { getServiceUrl } from '../api';
import RequestService from '../httpRequest';

export default {
    // 企业分页查询
    getOrganizationPage(params, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/organization/page`)
            .method('POST')
            .data(params)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('企业分页查询失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getOrganizationPage(params, callback)
                })
            }).send()
    },

    // 创建企业
    createOrganization(data, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/organization`)
            .method('POST')
            .data(data)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('创建企业失败:', err)
                RequestService.reAjaxFun(() => {
                    this.createOrganization(data, callback)
                })
            }).send()
    },

    // 更新企业
    updateOrganization(data, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/organization`)
            .method('PUT')
            .data(data)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('更新企业失败:', err)
                RequestService.reAjaxFun(() => {
                    this.updateOrganization(data, callback)
                })
            }).send()
    },

    // 删除企业
    deleteOrganization(id, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/organization/${id}`)
            .method('DELETE')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('删除企业失败:', err)
                RequestService.reAjaxFun(() => {
                    this.deleteOrganization(id, callback)
                })
            }).send()
    },

    // 获取企业详情
    getOrganizationDetail(id, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/organization/detail/${id}`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取企业详情失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getOrganizationDetail(id, callback)
                })
            }).send()
    },

    // 获取企业统计
    getOrganizationStatistics(id, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/organization/${id}/statistics`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取企业统计失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getOrganizationStatistics(id, callback)
                })
            }).send()
    }
}
