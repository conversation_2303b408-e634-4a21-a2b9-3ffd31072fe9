package xiaozhi.modules.device.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import xiaozhi.common.dao.BaseDao;
import xiaozhi.modules.device.entity.DeviceUsageStatisticsEntity;

/**
 * 设备使用统计DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Mapper
public interface DeviceUsageStatisticsDao extends BaseDao<DeviceUsageStatisticsEntity> {
    
    /**
     * 根据设备ID和日期范围获取统计数据
     * 
     * @param deviceId 设备ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<DeviceUsageStatisticsEntity> getStatisticsByDeviceAndDateRange(
        @Param("deviceId") String deviceId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate
    );
    
    /**
     * 根据用户ID和日期范围获取统计数据
     * 
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<DeviceUsageStatisticsEntity> getStatisticsByUserAndDateRange(
        @Param("userId") Long userId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate
    );
    
    /**
     * 根据智能体ID和日期范围获取统计数据
     * 
     * @param agentId 智能体ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<DeviceUsageStatisticsEntity> getStatisticsByAgentAndDateRange(
        @Param("agentId") String agentId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate
    );
    
    /**
     * 获取设备总体统计信息
     * 
     * @param deviceId 设备ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Map<String, Object> getDeviceOverallStatistics(
        @Param("deviceId") String deviceId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate
    );
    
    /**
     * 获取活跃设备排行
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行列表
     */
    List<Map<String, Object>> getActiveDevicesRanking(
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate,
        @Param("limit") Integer limit
    );
    
    /**
     * 获取租户设备使用统计
     * 
     * @param tenantId 租户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Map<String, Object> getTenantDeviceStatistics(
        @Param("tenantId") Long tenantId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate
    );
    
    /**
     * 记录设备使用情况
     * 
     * @param deviceId 设备ID
     * @param macAddress MAC地址
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param agentId 智能体ID
     * @param onlineDuration 在线时长
     * @param isSuccess 是否成功连接
     * @param dataTransferBytes 数据传输量
     */
    void recordDeviceUsage(
        @Param("deviceId") String deviceId,
        @Param("macAddress") String macAddress,
        @Param("userId") Long userId,
        @Param("tenantId") Long tenantId,
        @Param("agentId") String agentId,
        @Param("onlineDuration") Long onlineDuration,
        @Param("isSuccess") Boolean isSuccess,
        @Param("dataTransferBytes") Long dataTransferBytes
    );
}
