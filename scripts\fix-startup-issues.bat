@echo off
echo ========================================
echo 权限框架启动问题修复脚本
echo ========================================
echo.

echo 1. 检查Java版本...
java -version
echo.

echo 2. 检查Maven版本...
mvn -version
echo.

echo 3. 检查JAVA_HOME环境变量...
echo JAVA_HOME=%JAVA_HOME%
echo.

echo 4. 检查Java版本兼容性...
for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%i
)
echo Java版本: %JAVA_VERSION%

if "%JAVA_VERSION%" LSS "17" (
    echo [错误] 当前Java版本过低，Spring Boot 3.4.3需要Java 17+
    echo 请升级Java版本到17或更高版本
    echo.
    echo 下载地址:
    echo - Oracle JDK: https://www.oracle.com/java/technologies/downloads/
    echo - OpenJDK: https://adoptium.net/
    echo.
    pause
    exit /b 1
) else (
    echo [成功] Java版本兼容
)

echo.
echo 5. 检查数据库连接...
echo 请确保MySQL服务已启动，数据库xiaozhi_esp32_server已创建
echo.

echo 6. 检查Redis连接...
redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo [成功] Redis连接正常
) else (
    echo [警告] Redis连接失败，请启动Redis服务
)

echo.
echo 7. 清理并编译项目...
cd /d "%~dp0\..\main\manager-api"
call mvn clean compile
if %errorlevel% neq 0 (
    echo [错误] 编译失败，请检查Java版本和依赖
    pause
    exit /b 1
)

echo.
echo 8. 启动应用...
echo 正在启动Spring Boot应用...
call mvn spring-boot:run

pause
