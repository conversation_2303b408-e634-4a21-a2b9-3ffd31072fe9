<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xiaozhi.modules.sys.dao.SysRolePermissionDao">

    <!-- 根据角色ID删除权限关联 -->
    <delete id="deleteByRoleId">
        UPDATE sys_role_permission 
        SET deleted = 1, update_date = NOW()
        WHERE role_id = #{roleId} AND deleted = 0
    </delete>

    <!-- 根据权限ID删除角色关联 -->
    <delete id="deleteByPermissionId">
        UPDATE sys_role_permission 
        SET deleted = 1, update_date = NOW()
        WHERE permission_id = #{permissionId} AND deleted = 0
    </delete>

    <!-- 批量插入角色权限关联 -->
    <insert id="batchInsert">
        INSERT INTO sys_role_permission (role_id, permission_id, creator, create_date)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId}, #{item.permissionId}, #{item.creator}, #{item.createDate})
        </foreach>
    </insert>

    <!-- 根据角色ID获取权限ID列表 -->
    <select id="getPermissionIdsByRoleId" resultType="java.lang.Long">
        SELECT permission_id
        FROM sys_role_permission
        WHERE role_id = #{roleId}
          AND deleted = 0
    </select>

</mapper>
