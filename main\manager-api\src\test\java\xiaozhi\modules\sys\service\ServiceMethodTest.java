package xiaozhi.modules.sys.service;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import xiaozhi.modules.sys.entity.SysPermissionEntity;
import xiaozhi.modules.sys.entity.SysTenantEntity;

/**
 * Service方法测试
 * 验证BaseService中的方法是否正确定义
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@SpringBootTest
public class ServiceMethodTest {
    
    @Autowired(required = false)
    private SysPermissionService sysPermissionService;
    
    @Autowired(required = false)
    private SysTenantService sysTenantService;
    
    @Test
    public void testSysPermissionServiceMethods() {
        if (sysPermissionService != null) {
            // 测试 count 方法
            QueryWrapper<SysPermissionEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("status", 1);
            long count = sysPermissionService.count(wrapper);
            System.out.println("Permission count: " + count);
            
            // 测试 save 方法
            SysPermissionEntity permission = new SysPermissionEntity();
            permission.setPermission("test:permission:*");
            permission.setDescription("测试权限");
            permission.setDomain("test");
            permission.setAction("permission");
            permission.setInstance("*");
            permission.setStatus(1);
            
            // 注意：这里只是测试方法存在，不实际保存
            // boolean saved = sysPermissionService.save(permission);
            System.out.println("SysPermissionService.save method exists");
        }
    }
    
    @Test
    public void testSysTenantServiceMethods() {
        if (sysTenantService != null) {
            // 测试 list 方法
            List<SysTenantEntity> tenants = sysTenantService.list();
            System.out.println("Tenant list size: " + (tenants != null ? tenants.size() : 0));
            
            // 测试 getById 方法
            SysTenantEntity tenant = sysTenantService.getById(1L);
            System.out.println("Tenant by ID: " + (tenant != null ? tenant.getTenantName() : "null"));
        }
    }
}
