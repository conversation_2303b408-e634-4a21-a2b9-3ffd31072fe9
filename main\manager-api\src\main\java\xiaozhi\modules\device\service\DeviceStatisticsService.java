package xiaozhi.modules.device.service;

import java.util.List;
import java.util.Map;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.device.dto.DeviceStatisticsQueryDTO;
import xiaozhi.modules.device.dto.DeviceStatisticsResponseDTO;
import xiaozhi.modules.device.entity.DeviceUsageStatisticsEntity;

/**
 * 设备统计服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface DeviceStatisticsService extends BaseService<DeviceUsageStatisticsEntity> {
    
    /**
     * 获取设备使用统计
     *
     * @param queryDTO 查询条件
     * @return 统计数据
     */
    DeviceStatisticsResponseDTO getDeviceStatistics(DeviceStatisticsQueryDTO queryDTO);
    
    /**
     * 获取活跃设备排行
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行列表
     */
    List<Map<String, Object>> getActiveDevicesRanking(String startDate, String endDate, Integer limit);
    
    /**
     * 记录设备使用情况
     *
     * @param deviceId 设备ID
     * @param macAddress MAC地址
     * @param userId 用户ID
     * @param agentId 智能体ID
     * @param onlineDuration 在线时长
     * @param isSuccess 是否成功连接
     * @param dataTransferBytes 数据传输量
     */
    void recordDeviceUsage(String deviceId, String macAddress, Long userId, String agentId, 
                          Long onlineDuration, Boolean isSuccess, Long dataTransferBytes);
    
    /**
     * 获取租户设备统计概览
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    Map<String, Object> getTenantDeviceOverview(Long tenantId);
    
    /**
     * 获取设备总体统计信息
     *
     * @param deviceId 设备ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Map<String, Object> getDeviceOverallStatistics(String deviceId, String startDate, String endDate);
    
    /**
     * 获取用户设备使用统计
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<DeviceUsageStatisticsEntity> getUserDeviceStatistics(Long userId, String startDate, String endDate);
    
    /**
     * 获取智能体设备使用统计
     *
     * @param agentId 智能体ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<DeviceUsageStatisticsEntity> getAgentDeviceStatistics(String agentId, String startDate, String endDate);
}
