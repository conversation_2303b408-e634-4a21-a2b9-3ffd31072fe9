package xiaozhi.modules.sys.controller;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.page.PageData;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.sys.dto.TenantCreateRequestDTO;
import xiaozhi.modules.sys.dto.TenantDetailResponseDTO;
import xiaozhi.modules.sys.dto.TenantQueryRequestDTO;
import xiaozhi.modules.sys.dto.TenantUpdateRequestDTO;
import xiaozhi.modules.sys.entity.SysTenantEntity;
import xiaozhi.modules.sys.service.SysTenantService;

import javax.validation.Valid;

/**
 * 租户管理Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/sys/tenant")
@Tag(name = "租户管理", description = "多租户系统的租户管理相关接口，包括租户查询、企业关联等功能")
@AllArgsConstructor
public class SysTenantController {
    
    private final SysTenantService sysTenantService;
    
    @GetMapping("/list")
    @Operation(
            operationId = "listTenants",
            summary = "获取租户列表",
            description = "获取系统中所有租户的列表信息，支持分页查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功",
            content = @Content(schema = @Schema(implementation = Result.class))),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看租户列表")
    public Result<List<SysTenantEntity>> list() {
        List<SysTenantEntity> list = sysTenantService.list();
        return new Result<List<SysTenantEntity>>().ok(list);
    }
    
    @GetMapping("/{id}")
    @Operation(
            operationId = "getTenantInfo",
            summary = "获取租户详情",
            description = "根据租户ID获取租户的详细信息，包括租户配置、状态等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看租户详情")
    public Result<SysTenantEntity> info(
            @Parameter(description = "租户ID", required = true, example = "1")
            @PathVariable Long id) {
        SysTenantEntity tenant = sysTenantService.getById(id);
        return new Result<SysTenantEntity>().ok(tenant);
    }
    
    @GetMapping("/org/{orgId}")
    @Operation(
        operationId = "getTenantsByOrgId",
        summary = "根据企业ID获取租户列表",
        description = "获取指定企业下的所有租户列表，用于企业-租户关联查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "企业不存在")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看企业租户")
    public Result<List<SysTenantEntity>> getTenantsByOrgId(
            @Parameter(description = "企业ID", required = true, example = "1")
            @PathVariable Long orgId) {
        List<SysTenantEntity> tenants = sysTenantService.getTenantsByOrgId(orgId);
        return new Result<List<SysTenantEntity>>().ok(tenants);
    }
    
    @GetMapping("/expiring/{days}")
    @Operation(
        operationId = "getExpiringTenants",
        summary = "获取即将到期的租户",
        description = "获取在指定天数内即将到期的租户列表，用于到期提醒和续费管理"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看到期租户")
    public Result<List<SysTenantEntity>> getExpiringTenants(
            @Parameter(description = "天数", required = true, example = "30",
                schema = @Schema(minimum = "1", maximum = "365"))
            @PathVariable Integer days) {
        List<SysTenantEntity> tenants = sysTenantService.getExpiringTenants(days);
        return new Result<List<SysTenantEntity>>().ok(tenants);
    }

    @PostMapping("/page")
    @Operation(
        operationId = "pageTenants",
        summary = "分页查询租户",
        description = "根据查询条件分页获取租户详情列表，支持多种查询条件组合"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "tenant:view:*", description = "分页查询租户")
    public Result<PageData<TenantDetailResponseDTO>> page(
            @Parameter(description = "查询条件", required = true)
            @RequestBody @Valid TenantQueryRequestDTO queryRequest) {
        PageData<TenantDetailResponseDTO> page = sysTenantService.pageWithDetails(queryRequest);
        return new Result<PageData<TenantDetailResponseDTO>>().ok(page);
    }

    @GetMapping("/detail/{id}")
    @Operation(
        operationId = "getTenantDetail",
        summary = "获取租户详情",
        description = "根据租户ID获取租户的详细信息，包括统计数据、企业信息等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看租户详情")
    public Result<TenantDetailResponseDTO> detail(
            @Parameter(description = "租户ID", required = true, example = "1")
            @PathVariable Long id) {
        TenantDetailResponseDTO detail = sysTenantService.getTenantDetail(id);
        return new Result<TenantDetailResponseDTO>().ok(detail);
    }

    @PostMapping
    @Operation(
        operationId = "createTenant",
        summary = "创建租户",
        description = "创建新的租户，需要指定企业关联和基本配置信息"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "tenant:create:*", description = "创建租户")
    public Result<Void> create(
            @Parameter(description = "创建请求", required = true)
            @RequestBody @Valid TenantCreateRequestDTO createRequest) {
        sysTenantService.createTenant(createRequest);
        return new Result<>();
    }

    @PutMapping
    @Operation(
        operationId = "updateTenant",
        summary = "更新租户",
        description = "更新租户信息，包括基本信息、配置限制等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    @RequiresPermission(value = "tenant:update:*", description = "更新租户")
    public Result<Void> update(
            @Parameter(description = "更新请求", required = true)
            @RequestBody @Valid TenantUpdateRequestDTO updateRequest) {
        sysTenantService.updateTenant(updateRequest);
        return new Result<>();
    }

    @DeleteMapping("/{id}")
    @Operation(
        operationId = "deleteTenant",
        summary = "删除租户",
        description = "删除指定的租户，删除前会检查是否有关联数据"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "租户不存在"),
        @ApiResponse(responseCode = "400", description = "租户下还有关联数据，无法删除")
    })
    @RequiresPermission(value = "tenant:delete:*", description = "删除租户")
    public Result<Void> delete(
            @Parameter(description = "租户ID", required = true, example = "1")
            @PathVariable Long id) {
        sysTenantService.deleteTenant(id);
        return new Result<>();
    }

    @PutMapping("/{id}/status/{status}")
    @Operation(
        operationId = "updateTenantStatus",
        summary = "更新租户状态",
        description = "启用或禁用指定的租户"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    @RequiresPermission(value = "tenant:update:*", description = "更新租户状态")
    public Result<Void> updateStatus(
            @Parameter(description = "租户ID", required = true, example = "1")
            @PathVariable Long id,
            @Parameter(description = "状态", required = true, example = "1",
                schema = @Schema(allowableValues = {"0", "1"}))
            @PathVariable Integer status) {
        sysTenantService.updateTenantStatus(id, status);
        return new Result<>();
    }

    @GetMapping("/{id}/statistics")
    @Operation(
        operationId = "getTenantStatistics",
        summary = "获取租户统计信息",
        description = "获取租户的用户数、设备数、智能体数等统计信息"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看租户统计")
    public Result<Map<String, Object>> statistics(
            @Parameter(description = "租户ID", required = true, example = "1")
            @PathVariable Long id) {
        Map<String, Object> statistics = sysTenantService.getTenantStatistics(id);
        return new Result<Map<String, Object>>().ok(statistics);
    }
}
