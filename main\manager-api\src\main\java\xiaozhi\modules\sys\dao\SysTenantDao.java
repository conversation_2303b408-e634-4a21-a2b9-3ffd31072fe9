package xiaozhi.modules.sys.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import xiaozhi.common.dao.BaseDao;
import xiaozhi.modules.sys.entity.SysTenantEntity;

/**
 * 租户DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Mapper
public interface SysTenantDao extends BaseDao<SysTenantEntity> {
    
    /**
     * 根据租户编码获取租户信息
     * 
     * @param tenantCode 租户编码
     * @return 租户实体
     */
    SysTenantEntity getByTenantCode(@Param("tenantCode") String tenantCode);
    
    /**
     * 根据企业ID获取租户列表
     * 
     * @param orgId 企业ID
     * @return 租户实体列表
     */
    List<SysTenantEntity> getTenantsByOrgId(@Param("orgId") Long orgId);
    
    /**
     * 获取即将到期的租户列表
     * 
     * @param days 天数
     * @return 租户实体列表
     */
    List<SysTenantEntity> getExpiringTenants(@Param("days") Integer days);
}
