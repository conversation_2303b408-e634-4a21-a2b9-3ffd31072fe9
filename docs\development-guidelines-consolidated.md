# 小智项目开发规范与指导原则

## 1. 租户透明化开发规范

### 1.1 核心原则
- **完全透明**：租户处理对开发人员完全透明，不管在Controller、Service还是DAO层，开发人员都无需为租户开发额外逻辑
- **自动适应**：使用现有的Filter、Context、Interceptor机制进行系统自适应
- **数据隔离**：MyBatis拦截器自动处理租户数据过滤，确保数据安全

### 1.2 实现机制
- **TenantFilter**：自动设置租户上下文
- **TenantContext**：管理租户ID和用户类型
- **TenantInterceptor**：MyBatis拦截器自动添加租户过滤条件
- **平台管理员绕过**：平台管理员自动绕过租户限制

### 1.3 开发指导
```java
// ✅ 正确：直接调用Service，租户过滤自动处理
List<User> users = userService.list();

// ❌ 错误：手动处理租户逻辑
if (TenantContext.getTenantId() != null) {
    users = userService.getByTenantId(TenantContext.getTenantId());
}
```

## 2. API文档规范

### 2.1 @Operation注解规范
- **operationId必填**：使用英文，避免方法名相近时knife4j文档接口覆盖
- **summary**：简短的中文描述
- **description**：详细的中文说明

```java
@Operation(
    operationId = "createUser",           // 必填：英文操作ID
    summary = "创建用户",                  // 中文简述
    description = "创建新用户，支持多种用户类型" // 中文详述
)
```

### 2.2 命名规范
- **operationId命名**：动词+名词，如createUser、updateAgent、deleteDevice
- **避免重复**：确保同一Controller内operationId唯一
- **语义清晰**：操作意图明确，便于API文档阅读

## 3. 多租户架构规范

### 3.1 数据模型设计
- **租户字段**：新表必须包含tenant_id字段
- **审计字段**：包含creator、created_at、updater、updated_at、deleted
- **字段复用**：优先复用现有字段，避免创建冗余字段

### 3.2 权限设计
- **基于操作**：权限系统基于操作而非UI元素
- **RBAC模型**：角色-权限-用户三层模型
- **数据过滤**：agent级别的数据过滤

### 3.3 用户类型
- **平台管理员(1)**：管理所有租户和数据
- **租户管理员(2)**：管理本租户数据
- **普通用户(3)**：业务操作用户

## 4. 技术实现规范

### 4.1 依赖管理
- **使用包管理器**：npm、mvn、pip等，避免手动编辑配置文件
- **版本控制**：使用lock文件确保版本一致性
- **依赖隔离**：TTS等服务独立部署，避免依赖冲突

### 4.2 代码质量
- **统一Controller**：避免租户特定的Controller
- **DTO设计**：使用DTO而非Map传递数据
- **异常处理**：统一异常处理机制
- **日志规范**：使用@Slf4j注解

### 4.3 数据库设计
- **字段复用**：如update_date用于记录修改时间
- **索引优化**：避免低基数字段的单独索引
- **命名规范**：下划线命名，语义清晰

## 5. 项目结构规范

### 5.1 组件架构
- **xiaozhi-server**：Python服务，`python app.py`启动
- **manager-api**：Java服务，运行AdminApplication.java
- **manager-web**：Node.js前端，`npm run serve`启动

### 5.2 TTS提供商模式
- **独立部署**：TTS模型作为独立API服务
- **统一接口**：遵循main/xiaozhi-server/core/providers/tts/模式
- **配置管理**：在main/xiaozhi-server/config.yaml中配置

## 6. 内存存储设计

### 6.1 设计原则
- **按设备存储**：以设备MAC地址为唯一标识
- **简化设计**：只使用summaryMemory字段
- **避免冲突**：每设备通常只有一个活跃会话

### 6.2 数据结构
```sql
-- 设备内存表
CREATE TABLE device_memory (
    id BIGINT PRIMARY KEY,
    device_mac VARCHAR(17) UNIQUE,  -- MAC地址唯一标识
    summary_memory TEXT,            -- 摘要记忆
    tenant_id BIGINT,              -- 租户ID
    created_at TIMESTAMP,          -- 创建时间
    updated_at TIMESTAMP           -- 更新时间
);
```

## 7. 开发最佳实践

### 7.1 编码规范
- **空值检查**：使用StringUtils.isNotBlank()
- **类型安全**：明确类型转换
- **异常处理**：使用统一异常类型
- **注释完整**：方法和类都要有清晰注释

### 7.2 测试规范
- **单元测试**：核心业务逻辑必须有测试
- **集成测试**：API接口测试覆盖
- **租户测试**：验证数据隔离效果

### 7.3 部署规范
- **环境隔离**：开发、测试、生产环境分离
- **配置管理**：敏感信息使用环境变量
- **监控告警**：关键指标监控

## 8. 安全规范

### 8.1 数据安全
- **租户隔离**：自动数据过滤，防止跨租户访问
- **权限控制**：基于角色的访问控制
- **审计日志**：操作记录可追溯

### 8.2 API安全
- **参数验证**：使用@Valid注解验证
- **SQL注入防护**：使用MyBatis-Plus参数化查询
- **XSS防护**：输入输出过滤

## 9. 性能优化

### 9.1 查询优化
- **动态条件**：只添加有值的查询条件
- **索引利用**：合理设计数据库索引
- **分页查询**：大数据量使用分页

### 9.2 缓存策略
- **Redis缓存**：热点数据缓存
- **本地缓存**：配置信息缓存
- **缓存更新**：及时更新策略

## 10. 文档规范

### 10.1 API文档
- **Swagger注解**：完整的API文档注解
- **示例数据**：提供请求响应示例
- **错误码**：明确的错误码定义

### 10.2 代码文档
- **README**：项目说明和快速开始
- **CHANGELOG**：版本变更记录
- **架构图**：系统架构说明

---

**遵循以上规范，确保代码质量、系统安全和开发效率！**
