package xiaozhi.modules.agent.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.agent.dao.AgentUsageStatisticsDao;
import xiaozhi.modules.agent.dto.AgentStatisticsQueryDTO;
import xiaozhi.modules.agent.dto.AgentStatisticsResponseDTO;
import xiaozhi.modules.agent.entity.AgentUsageStatisticsEntity;
import xiaozhi.modules.agent.service.AgentStatisticsService;
import xiaozhi.modules.security.tenant.TenantContext;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 智能体统计服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Slf4j
@AllArgsConstructor
@Service
public class AgentStatisticsServiceImpl extends BaseServiceImpl<AgentUsageStatisticsDao, AgentUsageStatisticsEntity> implements AgentStatisticsService {
    
    private final AgentUsageStatisticsDao agentUsageStatisticsDao;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    
    @Override
    public AgentStatisticsResponseDTO getAgentStatistics(AgentStatisticsQueryDTO queryDTO) {
        try {
            Date startDate = dateFormat.parse(queryDTO.getStartDate());
            Date endDate = dateFormat.parse(queryDTO.getEndDate());
            
            // 获取总体统计信息
            Map<String, Object> overallStats = agentUsageStatisticsDao.getAgentOverallStatistics(
                queryDTO.getAgentId(), startDate, endDate);
            
            // 获取每日统计详情
            List<AgentUsageStatisticsEntity> dailyStats = agentUsageStatisticsDao.getStatisticsByAgentAndDateRange(
                queryDTO.getAgentId(), startDate, endDate);
            
            // 构建响应DTO
            AgentStatisticsResponseDTO response = new AgentStatisticsResponseDTO();
            response.setAgentId(queryDTO.getAgentId());
            response.setStartDate(startDate);
            response.setEndDate(endDate);
            
            if (overallStats != null) {
                response.setTotalUsageCount((Integer) overallStats.get("totalUsageCount"));
                response.setTotalDuration((Long) overallStats.get("totalDuration"));
                response.setAvgDuration((Long) overallStats.get("avgDuration"));
                response.setUserCount((Integer) overallStats.get("userCount"));
                response.setSuccessRate((Double) overallStats.get("successRate"));
                response.setErrorRate((Double) overallStats.get("errorRate"));
            }
            
            // 转换每日统计
            List<AgentStatisticsResponseDTO.DailyStatistics> dailyStatisticsList = new ArrayList<>();
            for (AgentUsageStatisticsEntity entity : dailyStats) {
                AgentStatisticsResponseDTO.DailyStatistics daily = new AgentStatisticsResponseDTO.DailyStatistics();
                daily.setDate(entity.getStatisticsDate());
                daily.setUsageCount(entity.getUsageCount());
                daily.setDuration(entity.getTotalDuration());
                daily.setSuccessCount(entity.getSuccessCount());
                daily.setFailureCount(entity.getFailureCount());
                dailyStatisticsList.add(daily);
            }
            response.setDailyStatistics(dailyStatisticsList);
            
            return response;
            
        } catch (ParseException e) {
            throw new RenException("日期格式错误");
        }
    }
    
    @Override
    public List<Map<String, Object>> getPopularAgentsRanking(String startDate, String endDate, Integer limit) {
        try {
            Date start = dateFormat.parse(startDate);
            Date end = dateFormat.parse(endDate);
            
            return agentUsageStatisticsDao.getPopularAgentsRanking(start, end, limit);
            
        } catch (ParseException e) {
            throw new RenException("日期格式错误");
        }
    }
    
    @Override
    public void recordAgentUsage(String agentId, Long userId, Long duration, Boolean isSuccess) {
        Long tenantId = TenantContext.getTenantId();
        
        agentUsageStatisticsDao.recordAgentUsage(agentId, userId, tenantId, duration, isSuccess);
    }
    
    @Override
    public Map<String, Object> getTenantAgentOverview(Long tenantId) {
        // 获取最近30天的统计
        Date endDate = new Date();
        Date startDate = new Date(endDate.getTime() - 30L * 24 * 60 * 60 * 1000);
        
        return agentUsageStatisticsDao.getTenantAgentStatistics(tenantId, startDate, endDate);
    }
    
    @Override
    public Map<String, Object> getAgentOverallStatistics(String agentId, String startDate, String endDate) {
        try {
            Date start = dateFormat.parse(startDate);
            Date end = dateFormat.parse(endDate);
            
            return agentUsageStatisticsDao.getAgentOverallStatistics(agentId, start, end);
            
        } catch (ParseException e) {
            throw new RenException("日期格式错误");
        }
    }
    
    @Override
    public List<AgentUsageStatisticsEntity> getUserAgentStatistics(Long userId, String startDate, String endDate) {
        try {
            Date start = dateFormat.parse(startDate);
            Date end = dateFormat.parse(endDate);
            
            return agentUsageStatisticsDao.getStatisticsByUserAndDateRange(userId, start, end);
            
        } catch (ParseException e) {
            throw new RenException("日期格式错误");
        }
    }
}
