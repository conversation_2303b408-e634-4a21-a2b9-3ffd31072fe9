package xiaozhi.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 智能体分配查询DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "智能体分配查询")
public class AgentAssignmentQueryDTO {

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private String page = "1";

    @Schema(description = "每页数量", example = "10")
    @Min(value = 1, message = "每页数量必须大于0")
    private String limit = "10";
    
    @Schema(description = "智能体ID", example = "agent123")
    private String agentId;
    
    @Schema(description = "用户ID", example = "1")
    private Long userId;
    
    @Schema(description = "分配状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;
    
    @Schema(description = "分配开始时间", example = "2025-01-01")
    private String assignedStartDate;
    
    @Schema(description = "分配结束时间", example = "2025-01-31")
    private String assignedEndDate;
    
    @Schema(description = "分配者ID", example = "1")
    private Long assignedBy;
}
