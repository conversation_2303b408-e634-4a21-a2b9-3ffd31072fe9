const { execSync } = require('child_process');
const path = require('path');

console.log('正在启动开发服务器...');
console.log('Node.js 版本:', process.version);
console.log('当前目录:', process.cwd());

try {
  // 设置环境变量
  process.env.NODE_ENV = 'development';
  
  // 启动开发服务器
  execSync('npx vue-cli-service serve --host 0.0.0.0 --port 8080', {
    stdio: 'inherit',
    cwd: __dirname
  });
} catch (error) {
  console.error('启动失败:', error.message);
  process.exit(1);
}
