package xiaozhi.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 智能体分配请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "智能体分配请求")
public class AgentAssignmentRequestDTO {
    
    @Schema(description = "智能体ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "智能体ID列表不能为空")
    private List<String> agentIds;
    
    @Schema(description = "用户ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "用户ID列表不能为空")
    private List<Long> userIds;
    
    @Schema(description = "备注", example = "批量分配智能体")
    @Size(max = 500, message = "备注长度不能超过500字符")
    private String remark;
}
