package xiaozhi.modules.security.tenant;

import java.util.List;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;

import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;

/**
 * 租户过滤拦截器
 * 自动为需要租户隔离的表添加租户过滤条件
 *
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public class TenantInterceptor implements InnerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(TenantInterceptor.class);
    
    /**
     * 需要租户隔离的表名列表
     */
    private static final List<String> TENANT_TABLES = List.of(
        "sys_user", "ai_agent", "ai_device", "sys_role", "sys_user_role"
    );

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) {
        // 平台管理员不进行租户过滤
        if (TenantContext.isPlatformAdmin()) {
            return;
        }

        // 获取当前租户ID
        Long tenantId = TenantContext.getTenantId();
        if (tenantId == null) {
            return;
        }

        String originalSql = boundSql.getSql();
        try {
            String modifiedSql = addTenantFilter(originalSql, tenantId, SqlCommandType.SELECT);
            if (!originalSql.equals(modifiedSql)) {
                PluginUtils.mpBoundSql(boundSql).sql(modifiedSql);
                logger.debug("租户过滤 - 原SQL: {}", originalSql);
                logger.debug("租户过滤 - 新SQL: {}", modifiedSql);
            }
        } catch (Exception e) {
            logger.warn("租户过滤失败，使用原SQL: {}", e.getMessage());
        }
    }

    @Override
    public void beforeUpdate(Executor executor, MappedStatement ms, Object parameter) {
        // 平台管理员不进行租户过滤
        if (TenantContext.isPlatformAdmin()) {
            return;
        }

        // 获取当前租户ID
        Long tenantId = TenantContext.getTenantId();
        if (tenantId == null) {
            return;
        }

        BoundSql boundSql = ms.getBoundSql(parameter);
        String originalSql = boundSql.getSql();

        try {
            String modifiedSql = addTenantFilter(originalSql, tenantId, ms.getSqlCommandType());
            if (!originalSql.equals(modifiedSql)) {
                PluginUtils.mpBoundSql(boundSql).sql(modifiedSql);
                logger.debug("租户过滤 - 原SQL: {}", originalSql);
                logger.debug("租户过滤 - 新SQL: {}", modifiedSql);
            }
        } catch (Exception e) {
            logger.warn("租户过滤失败，使用原SQL: {}", e.getMessage());
        }
    }
    
    /**
     * 为SQL添加租户过滤条件
     * 
     * @param sql 原始SQL
     * @param tenantId 租户ID
     * @param sqlCommandType SQL命令类型
     * @return 修改后的SQL
     */
    private String addTenantFilter(String sql, Long tenantId, SqlCommandType sqlCommandType) {
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            
            switch (sqlCommandType) {
                case SELECT:
                    if (statement instanceof Select) {
                        addTenantFilterToSelect((Select) statement, tenantId);
                    }
                    break;
                case UPDATE:
                    if (statement instanceof Update) {
                        addTenantFilterToUpdate((Update) statement, tenantId);
                    }
                    break;
                case DELETE:
                    if (statement instanceof Delete) {
                        addTenantFilterToDelete((Delete) statement, tenantId);
                    }
                    break;
                case INSERT:
                    // INSERT语句通常不需要添加租户过滤条件
                    // 租户ID应该在业务层设置
                    break;
                default:
                    break;
            }
            
            return statement.toString();
        } catch (JSQLParserException e) {
            logger.warn("解析SQL失败: {}", e.getMessage());
            return sql;
        }
    }
    
    /**
     * 为SELECT语句添加租户过滤条件
     */
    private void addTenantFilterToSelect(Select select, Long tenantId) {
        if (select.getSelectBody() instanceof PlainSelect) {
            PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
            String tableName = getTableName(plainSelect.getFromItem().toString());
            
            if (TENANT_TABLES.contains(tableName)) {
                Expression tenantCondition = createTenantCondition(tenantId);
                Expression whereExpression = plainSelect.getWhere();
                
                if (whereExpression == null) {
                    plainSelect.setWhere(tenantCondition);
                } else {
                    plainSelect.setWhere(new AndExpression(whereExpression, tenantCondition));
                }
            }
        }
    }
    
    /**
     * 为UPDATE语句添加租户过滤条件
     */
    private void addTenantFilterToUpdate(Update update, Long tenantId) {
        String tableName = getTableName(update.getTable().getName());
        
        if (TENANT_TABLES.contains(tableName)) {
            Expression tenantCondition = createTenantCondition(tenantId);
            Expression whereExpression = update.getWhere();
            
            if (whereExpression == null) {
                update.setWhere(tenantCondition);
            } else {
                update.setWhere(new AndExpression(whereExpression, tenantCondition));
            }
        }
    }
    
    /**
     * 为DELETE语句添加租户过滤条件
     */
    private void addTenantFilterToDelete(Delete delete, Long tenantId) {
        String tableName = getTableName(delete.getTable().getName());
        
        if (TENANT_TABLES.contains(tableName)) {
            Expression tenantCondition = createTenantCondition(tenantId);
            Expression whereExpression = delete.getWhere();
            
            if (whereExpression == null) {
                delete.setWhere(tenantCondition);
            } else {
                delete.setWhere(new AndExpression(whereExpression, tenantCondition));
            }
        }
    }
    
    /**
     * 创建租户过滤条件
     */
    private Expression createTenantCondition(Long tenantId) {
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(new Column("tenant_id"));
        equalsTo.setRightExpression(new LongValue(tenantId));
        return equalsTo;
    }
    
    /**
     * 获取表名（去除别名）
     */
    private String getTableName(String tableExpression) {
        String tableName = tableExpression.trim();
        // 去除反引号
        tableName = tableName.replace("`", "");
        // 如果有别名，取第一部分
        if (tableName.contains(" ")) {
            tableName = tableName.split(" ")[0];
        }
        return tableName;
    }
    
}
