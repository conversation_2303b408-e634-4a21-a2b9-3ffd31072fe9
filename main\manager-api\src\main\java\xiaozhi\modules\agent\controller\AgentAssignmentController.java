package xiaozhi.modules.agent.controller;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.page.PageData;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.agent.dto.AgentAssignmentQueryDTO;
import xiaozhi.modules.agent.dto.AgentAssignmentRequestDTO;
import xiaozhi.modules.agent.dto.AgentDTO;
import xiaozhi.modules.agent.entity.AgentAssignmentEntity;
import xiaozhi.modules.agent.service.AgentService;
import xiaozhi.modules.security.annotation.RequiresPermission;

import javax.validation.Valid;

/**
 * 智能体分配管理Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@RestController
@RequestMapping("/agent/assignment")
@Tag(name = "智能体分配管理", description = "智能体与用户的分配关系管理")
public class AgentAssignmentController {
    
    private final AgentService agentService;
    
    @PostMapping("/assign")
    @Operation(
        operationId = "assignAgentsToUsers",
        summary = "分配智能体给用户",
        description = "将指定的智能体分配给指定的用户，支持批量分配"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "分配成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "agent:assign:*", description = "分配智能体")
    public Result<Void> assignAgentsToUsers(
            @Parameter(description = "分配请求", required = true)
            @RequestBody @Valid AgentAssignmentRequestDTO assignmentRequest) {
        agentService.assignAgentsToUsers(assignmentRequest);
        return new Result<>();
    }
    
    @DeleteMapping("/unassign")
    @Operation(
        operationId = "unassignAgentsFromUsers",
        summary = "取消智能体分配",
        description = "取消指定智能体与用户的分配关系，支持批量取消"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "取消成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "agent:assign:*", description = "取消智能体分配")
    public Result<Void> unassignAgentsFromUsers(
            @Parameter(description = "取消分配请求", required = true)
            @RequestBody @Valid AgentAssignmentRequestDTO assignmentRequest) {
        agentService.unassignAgentsFromUsers(assignmentRequest);
        return new Result<>();
    }
    
    @PostMapping("/page")
    @Operation(
        operationId = "pageAgentAssignments",
        summary = "分页查询智能体分配记录",
        description = "根据查询条件分页获取智能体分配记录"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "agent:view:*", description = "查看智能体分配")
    public Result<PageData<AgentAssignmentEntity>> pageAssignments(
            @Parameter(description = "查询条件", required = true)
            @RequestBody @Valid AgentAssignmentQueryDTO queryDTO) {
        PageData<AgentAssignmentEntity> page = agentService.pageAssignments(queryDTO);
        return new Result<PageData<AgentAssignmentEntity>>().ok(page);
    }
    
    @GetMapping("/user/{userId}/agents")
    @Operation(
        operationId = "getAssignedAgentsByUserId",
        summary = "获取用户已分配的智能体",
        description = "获取指定用户已分配的智能体列表"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "agent:view:*", description = "查看用户智能体")
    public Result<List<AgentDTO>> getAssignedAgentsByUserId(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {
        List<AgentDTO> agents = agentService.getAssignedAgentsByUserId(userId);
        return new Result<List<AgentDTO>>().ok(agents);
    }
    
    @GetMapping("/agent/{agentId}/users")
    @Operation(
        operationId = "getAssignedUsersByAgentId",
        summary = "获取智能体已分配的用户",
        description = "获取指定智能体已分配的用户ID列表"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "智能体不存在")
    })
    @RequiresPermission(value = "agent:view:*", description = "查看智能体用户")
    public Result<List<Long>> getAssignedUsersByAgentId(
            @Parameter(description = "智能体ID", required = true, example = "agent123")
            @PathVariable String agentId) {
        List<Long> userIds = agentService.getAssignedUsersByAgentId(agentId);
        return new Result<List<Long>>().ok(userIds);
    }
}
