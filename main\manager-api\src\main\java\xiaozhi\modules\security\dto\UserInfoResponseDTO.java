package xiaozhi.modules.security.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 用户信息响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "用户信息响应")
public class UserInfoResponseDTO {
    
    @Schema(description = "用户ID", example = "1")
    private Long userId;
    
    @Schema(description = "用户名", example = "admin")
    private String username;
    
    @Schema(description = "真实姓名", example = "管理员")
    private String realName;
    
    @Schema(description = "头像URL", example = "http://example.com/avatar.jpg")
    private String headUrl;
    
    @Schema(description = "性别", example = "0")
    private Integer gender;
    
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "手机号", example = "13800138000")
    private String mobile;
    
    @Schema(description = "用户类型", example = "1")
    private Integer userType;
    
    @Schema(description = "状态", example = "1")
    private Integer status;
    
    @Schema(description = "租户ID", example = "1")
    private Long tenantId;
    
    @Schema(description = "租户名称", example = "测试租户")
    private String tenantName;
    
    @Schema(description = "部门ID", example = "1")
    private Long deptId;
    
    @Schema(description = "部门名称", example = "技术部")
    private String deptName;
    
    @Schema(description = "创建时间")
    private Date createDate;
    
    @Schema(description = "角色列表")
    private List<String> roles;
    
    @Schema(description = "权限列表")
    private Set<String> permissions;
    
    @Schema(description = "是否平台管理员", example = "true")
    private Boolean isPlatformAdmin;
    
    @Schema(description = "是否租户管理员", example = "false")
    private Boolean isTenantAdmin;
    
    @Schema(description = "是否超级管理员", example = "false")
    private Boolean isSuperAdmin;
}
