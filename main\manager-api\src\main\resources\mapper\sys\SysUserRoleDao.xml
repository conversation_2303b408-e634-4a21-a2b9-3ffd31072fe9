<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xiaozhi.modules.sys.dao.SysUserRoleDao">

    <!-- 根据用户ID删除角色关联 -->
    <delete id="deleteByUserId">
        UPDATE sys_user_role 
        SET deleted = 1, update_date = NOW()
        WHERE user_id = #{userId} AND deleted = 0
    </delete>

    <!-- 根据角色ID删除用户关联 -->
    <delete id="deleteByRoleId">
        UPDATE sys_user_role 
        SET deleted = 1, update_date = NOW()
        WHERE role_id = #{roleId} AND deleted = 0
    </delete>

    <!-- 批量插入用户角色关联 -->
    <insert id="batchInsert">
        INSERT INTO sys_user_role (user_id, role_id, creator, create_date)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.roleId}, #{item.creator}, #{item.createDate})
        </foreach>
    </insert>

    <!-- 根据用户ID获取角色ID列表 -->
    <select id="getRoleIdsByUserId" resultType="java.lang.Long">
        SELECT role_id
        FROM sys_user_role
        WHERE user_id = #{userId}
          AND deleted = 0
    </select>

</mapper>
