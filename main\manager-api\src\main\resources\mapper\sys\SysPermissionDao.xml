<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xiaozhi.modules.sys.dao.SysPermissionDao">

    <!-- 根据用户ID获取权限列表 -->
    <select id="getUserPermissions" resultType="java.lang.String">
        SELECT DISTINCT p.permission
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.status = 1
          AND p.deleted = 0
          AND rp.deleted = 0
          AND ur.deleted = 0
    </select>

    <!-- 根据角色ID获取权限列表 -->
    <select id="getPermissionsByRoleId" resultType="xiaozhi.modules.sys.entity.SysPermissionEntity">
        SELECT p.*
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
          AND p.status = 1
          AND p.deleted = 0
          AND rp.deleted = 0
        ORDER BY p.domain, p.action
    </select>

    <!-- 根据权限域和操作获取权限列表 -->
    <select id="getPermissionsByDomainAndAction" resultType="xiaozhi.modules.sys.entity.SysPermissionEntity">
        SELECT *
        FROM sys_permission
        WHERE domain = #{domain}
          AND action = #{action}
          AND status = 1
          AND deleted = 0
        ORDER BY instance
    </select>

</mapper>
