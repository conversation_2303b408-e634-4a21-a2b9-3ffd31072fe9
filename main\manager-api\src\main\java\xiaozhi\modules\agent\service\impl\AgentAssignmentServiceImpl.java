package xiaozhi.modules.agent.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.AllArgsConstructor;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.PageData;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.agent.dao.AgentAssignmentDao;
import xiaozhi.modules.agent.dto.AgentAssignmentQueryDTO;
import xiaozhi.modules.agent.dto.AgentAssignmentRequestDTO;
import xiaozhi.modules.agent.entity.AgentAssignmentEntity;
import xiaozhi.modules.agent.service.AgentAssignmentService;
import xiaozhi.modules.security.tenant.TenantContext;
import xiaozhi.modules.security.user.SecurityUser;

/**
 * 智能体分配服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class AgentAssignmentServiceImpl extends BaseServiceImpl<AgentAssignmentDao, AgentAssignmentEntity> implements AgentAssignmentService {
    
    private final AgentAssignmentDao agentAssignmentDao;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignAgentsToUsers(AgentAssignmentRequestDTO assignmentRequest) {
        Long currentUserId = SecurityUser.getUserId();
        Long tenantId = TenantContext.getTenantId();
        Date now = new Date();
        
        List<AgentAssignmentEntity> assignments = new ArrayList<>();
        
        for (String agentId : assignmentRequest.getAgentIds()) {
            for (Long userId : assignmentRequest.getUserIds()) {
                // 检查是否已经分配
                if (!isAgentAssignedToUser(agentId, userId)) {
                    AgentAssignmentEntity assignment = new AgentAssignmentEntity();
                    assignment.setAgentId(agentId);
                    assignment.setUserId(userId);
                    assignment.setTenantId(tenantId);
                    assignment.setStatus(1); // 已分配
                    assignment.setAssignedAt(now);
                    assignment.setAssignedBy(currentUserId);
                    assignment.setRemark(assignmentRequest.getRemark());
                    assignment.setCreator(currentUserId);
                    assignment.setCreatedAt(now);
                    assignment.setUpdater(currentUserId);
                    assignment.setUpdatedAt(now);
                    assignment.setDeleted(0);
                    
                    assignments.add(assignment);
                }
            }
        }
        
        if (!assignments.isEmpty()) {
            agentAssignmentDao.batchInsertAssignments(assignments);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unassignAgentsFromUsers(AgentAssignmentRequestDTO assignmentRequest) {
        Long currentUserId = SecurityUser.getUserId();
        
        int affected = agentAssignmentDao.batchUnassignAgents(
            assignmentRequest.getAgentIds(),
            assignmentRequest.getUserIds(),
            currentUserId
        );
        
        if (affected == 0) {
            throw new RenException("没有找到可取消的分配记录");
        }
    }
    
    @Override
    public PageData<AgentAssignmentEntity> pageAssignments(AgentAssignmentQueryDTO queryDTO) {
        Map<String, Object> params = Map.of(
            "page", queryDTO.getPage(),
            "limit", queryDTO.getLimit()
        );
        
        QueryWrapper<AgentAssignmentEntity> wrapper = buildQueryWrapper(queryDTO);
        
        IPage<AgentAssignmentEntity> page = baseDao.selectPage(
            getPage(params, "assigned_at", false),
            wrapper
        );
        
        return new PageData<>(page.getRecords(), page.getTotal());
    }
    
    @Override
    public boolean isAgentAssignedToUser(String agentId, Long userId) {
        return agentAssignmentDao.isAgentAssignedToUser(agentId, userId);
    }
    
    @Override
    public List<String> getAssignedAgentIdsByUserId(Long userId) {
        return agentAssignmentDao.getAssignedAgentIdsByUserId(userId);
    }
    
    @Override
    public List<Long> getAssignedUserIdsByAgentId(String agentId) {
        return agentAssignmentDao.getAssignedUserIdsByAgentId(agentId);
    }
    
    @Override
    public List<AgentAssignmentEntity> getAssignmentsByAgentId(String agentId) {
        return agentAssignmentDao.getAssignmentsByAgentId(agentId);
    }
    
    @Override
    public List<AgentAssignmentEntity> getAssignmentsByUserId(Long userId) {
        return agentAssignmentDao.getAssignmentsByUserId(userId);
    }
    
    /**
     * 构建查询条件
     */
    private QueryWrapper<AgentAssignmentEntity> buildQueryWrapper(AgentAssignmentQueryDTO queryDTO) {
        QueryWrapper<AgentAssignmentEntity> wrapper = new QueryWrapper<>();
        
        // 智能体ID
        if (StringUtils.isNotBlank(queryDTO.getAgentId())) {
            wrapper.eq("agent_id", queryDTO.getAgentId());
        }
        
        // 用户ID
        if (queryDTO.getUserId() != null) {
            wrapper.eq("user_id", queryDTO.getUserId());
        }
        
        // 分配状态
        if (queryDTO.getStatus() != null) {
            wrapper.eq("status", queryDTO.getStatus());
        }
        
        // 分配时间范围
        if (StringUtils.isNotBlank(queryDTO.getAssignedStartDate())) {
            wrapper.ge("assigned_at", queryDTO.getAssignedStartDate());
        }
        if (StringUtils.isNotBlank(queryDTO.getAssignedEndDate())) {
            wrapper.le("assigned_at", queryDTO.getAssignedEndDate());
        }
        
        // 分配者
        if (queryDTO.getAssignedBy() != null) {
            wrapper.eq("assigned_by", queryDTO.getAssignedBy());
        }
        
        // 排除已删除的记录
        wrapper.eq("deleted", 0);
        
        // 默认按分配时间倒序排列
        wrapper.orderByDesc("assigned_at");
        
        return wrapper;
    }
}
