package xiaozhi.modules.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 企业详情响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "企业详情响应")
public class OrganizationDetailResponseDTO {
    
    @Schema(description = "企业ID", example = "1")
    private Long id;
    
    @Schema(description = "企业编码", example = "ORG001")
    private String orgCode;
    
    @Schema(description = "企业名称", example = "测试企业")
    private String orgName;
    
    @Schema(description = "企业类型", example = "1")
    private Integer orgType;
    
    @Schema(description = "企业类型名称", example = "普通企业")
    private String orgTypeName;
    
    @Schema(description = "联系人", example = "张三")
    private String contactPerson;
    
    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;
    
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;
    
    @Schema(description = "企业地址", example = "北京市朝阳区")
    private String address;
    
    @Schema(description = "状态", example = "1")
    private Integer status;
    
    @Schema(description = "状态名称", example = "启用")
    private String statusName;
    
    @Schema(description = "到期时间", example = "2025-12-31 23:59:59")
    private Date expireDate;
    
    @Schema(description = "是否即将到期", example = "false")
    private Boolean isExpiring;
    
    @Schema(description = "剩余天数", example = "365")
    private Integer remainingDays;
    
    @Schema(description = "租户数量", example = "5")
    private Integer tenantCount;
    
    @Schema(description = "用户总数", example = "50")
    private Integer totalUsers;
    
    @Schema(description = "设备总数", example = "500")
    private Integer totalDevices;
    
    @Schema(description = "智能体总数", example = "25")
    private Integer totalAgents;
    
    @Schema(description = "备注", example = "测试企业备注")
    private String remark;
    
    @Schema(description = "创建时间", example = "2025-01-15 10:00:00")
    private Date createDate;
    
    @Schema(description = "更新时间", example = "2025-01-15 10:00:00")
    private Date updateDate;
    
    @Schema(description = "创建者", example = "1")
    private Long creator;
    
    @Schema(description = "更新者", example = "1")
    private Long updater;
}
