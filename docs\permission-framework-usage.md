# 权限框架使用指南

## 1. 概述

本权限框架基于Shiro实现，采用RBAC（基于角色的访问控制）模型，支持多租户数据隔离。权限字符串采用`domain:action:instance`格式，支持通配符和租户权限。

## 2. 权限字符串格式

### 2.1 基本格式
```
domain:action:instance
```

- **domain**: 权限域（资源类型），如：organization、tenant、user、agent、device
- **action**: 操作类型，如：view、create、update、delete、bind、unbind
- **instance**: 实例标识，可选，支持通配符*

### 2.2 权限示例
```java
// 查看所有企业
"organization:view:*"

// 查看租户内用户
"user:view:tenant"

// 绑定租户设备
"device:bind:tenant"

// 查看特定设备
"device:view:12345"

// 超级权限
"*:*:*"
```

## 3. 注解使用

### 3.1 @RequiresPermission
用于方法级权限校验：

```java
@RequiresPermission(value = "device:view:*", description = "查看设备权限")
public List<Device> getDeviceList() {
    // 业务逻辑
}

@RequiresPermission(value = "device:bind:tenant", tenant = true, description = "绑定设备权限")
public void bindDevice(String deviceId, String agentId) {
    // 业务逻辑
}
```

### 3.2 @DataScope
用于数据权限控制：

```java
@DataScope(tenantFilter = true, userFilter = false)
public List<Device> getMyDevices() {
    // 自动添加租户过滤条件
}
```

## 4. 编程式权限校验

### 4.1 使用PermissionUtils
```java
@Autowired
private PermissionUtils permissionUtils;

// 检查权限
if (permissionUtils.hasPermission("device:view:*")) {
    // 有权限的逻辑
}

// 检查租户权限
if (permissionUtils.hasTenantPermission("device:bind:tenant")) {
    // 有租户权限的逻辑
}

// 检查多个权限（OR关系）
if (permissionUtils.hasAnyPermission("device:view:*", "device:view:tenant")) {
    // 有任意一个权限的逻辑
}
```

### 4.2 使用Shiro原生API
```java
Subject subject = SecurityUtils.getSubject();

// 检查权限
if (subject.isPermitted("device:view:*")) {
    // 有权限的逻辑
}

// 检查角色
if (subject.hasRole("PLATFORM_ADMIN")) {
    // 有角色的逻辑
}
```

## 5. 租户上下文

### 5.1 自动设置
系统会自动从以下来源设置租户上下文：
1. 请求头 `X-Tenant-Id`
2. 当前登录用户的租户ID

### 5.2 手动设置
```java
// 设置租户上下文
TenantContext.setTenantId(tenantId);

try {
    // 业务逻辑
} finally {
    // 清除租户上下文
    TenantContext.clear();
}
```

## 6. 数据库自动过滤

### 6.1 支持的表
系统会自动为以下表添加租户过滤条件：
- sys_user
- ai_agent
- ai_device
- sys_role
- sys_user_role

### 6.2 SQL示例
原始SQL：
```sql
SELECT * FROM sys_user WHERE status = 1
```

自动添加租户过滤后：
```sql
SELECT * FROM sys_user WHERE status = 1 AND tenant_id = 1
```

## 7. 预设角色和权限

### 7.1 平台角色
- **PLATFORM_ADMIN**: 平台管理员，拥有所有权限
- **PLATFORM_OPERATOR**: 平台运营，负责企业管理

### 7.2 租户角色
- **TENANT_ADMIN**: 租户管理员，管理租户内所有资源
- **TENANT_DEVICE_MANAGER**: 设备管理员，负责设备管理
- **TENANT_OPERATOR**: 租户运营，负责报表统计
- **TENANT_USER**: 普通用户，基础查看权限

## 8. 最佳实践

### 8.1 Controller层
```java
@RestController
@RequestMapping("/api/device")
public class DeviceController {
    
    @GetMapping("/list")
    @RequiresPermission(value = "device:view:tenant", tenant = true)
    public Result<List<Device>> list() {
        // 自动应用租户过滤
        return deviceService.list();
    }
    
    @PostMapping("/bind")
    @RequiresPermission(value = "device:bind:tenant", tenant = true)
    public Result<Void> bind(@RequestBody BindRequest request) {
        return deviceService.bind(request);
    }
}
```

### 8.2 Service层
```java
@Service
public class DeviceService {
    
    @Autowired
    private PermissionUtils permissionUtils;
    
    public List<Device> getDeviceList() {
        // 根据权限返回不同数据
        if (permissionUtils.hasPermission("device:view:*")) {
            // 平台管理员，查看所有设备
            return deviceMapper.selectAll();
        } else if (permissionUtils.hasTenantPermission("device:view:tenant")) {
            // 租户用户，只查看租户设备（自动过滤）
            return deviceMapper.selectList(null);
        } else {
            throw new UnauthorizedException("无权限访问设备信息");
        }
    }
}
```

## 9. 测试接口

系统提供了测试接口用于验证权限框架：

- `GET /test/permission/platform/admin` - 平台管理员权限测试
- `GET /test/permission/tenant/admin` - 租户管理员权限测试
- `GET /test/permission/device/manager` - 设备管理员权限测试
- `GET /test/permission/tenant/user` - 租户用户权限测试
- `GET /test/permission/system/config` - 系统配置权限测试
- `GET /test/permission/public` - 公开接口测试

## 10. 故障排除

### 10.1 权限验证失败
1. 检查用户是否已登录
2. 检查用户是否有对应的角色
3. 检查角色是否有对应的权限
4. 检查权限字符串是否正确

### 10.2 租户过滤不生效
1. 检查租户上下文是否正确设置
2. 检查表名是否在租户表列表中
3. 检查SQL是否被正确解析和修改

### 10.3 调试技巧
```java
// 开启调试日志
logging.level.xiaozhi.modules.security = DEBUG

// 查看当前用户权限
Set<String> permissions = permissionUtils.getUserPermissions();
logger.debug("当前用户权限: {}", permissions);

// 查看租户上下文
Long tenantId = TenantContext.getTenantId();
logger.debug("当前租户ID: {}", tenantId);
```
