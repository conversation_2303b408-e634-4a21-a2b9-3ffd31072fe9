# 启动问题解决方案

## 1. 问题分析

### 1.1 原始错误
```
Error creating bean with name 's<PERSON><PERSON><PERSON><PERSON>' defined in class path resource [xiaozhi/modules/security/config/ShiroConfig.class]: Unsatisfied dependency expressed through method 'shirFilter' parameter 1: Error creating bean with name 'sysParamsServiceImpl': Unsatisfied dependency expressed through field 'baseDao': Error creating bean with name 'sysParamsDao' defined in file [...]: Cannot resolve reference to bean 'sqlSessionTemplate' while setting bean property 'sqlSessionTemplate'
```

### 1.2 真实问题
通过进一步测试发现，真正的问题是**Java版本不兼容**：

```
错误: 无法访问org.springframework.boot.SpringApplication
错误原因: E:\apache-maven-3.6.3\repo\org\springframework\boot\spring-boot\3.4.3\spring-boot-3.4.3.jar(org/springframework/boot/SpringApplication.class)
类文件具有错误的版本 61.0, 应为 52.0
```

- **Spring Boot 3.4.3** 需要 **Java 17+** (class file version 61.0)
- **当前环境使用** **Java 8** (class file version 52.0)

## 2. 解决方案

### 2.1 升级Java版本（推荐）

#### 方案A：升级到Java 17
```bash
# 下载并安装Java 17
# 设置JAVA_HOME环境变量
set JAVA_HOME=C:\Program Files\Java\jdk-17
set PATH=%JAVA_HOME%\bin;%PATH%

# 验证Java版本
java -version
javac -version
```

#### 方案B：升级到Java 21
```bash
# 下载并安装Java 21
# 设置JAVA_HOME环境变量
set JAVA_HOME=C:\Program Files\Java\jdk-21
set PATH=%JAVA_HOME%\bin;%PATH%

# 验证Java版本
java -version
javac -version
```

### 2.2 降级Spring Boot版本（不推荐）

如果无法升级Java版本，可以考虑降级Spring Boot到2.x版本，但这会影响很多功能。

## 3. 权限框架代码修复

在解决Java版本问题的同时，我们已经修复了权限框架中的配置冲突：

### 3.1 修复的问题

1. **MyBatis配置冲突**：
   - 删除了重复的`TenantConfig`类
   - 将`TenantInterceptor`集成到现有的`MybatisPlusConfig`中

2. **Shiro配置问题**：
   - 修复了`ShiroConfig`中的`TenantFilter`依赖注入问题

3. **循环依赖问题**：
   - 暂时禁用了`PermissionInitService`，避免启动时的循环依赖

### 3.2 修改的文件

```
main/manager-api/src/main/java/xiaozhi/common/config/MybatisPlusConfig.java
main/manager-api/src/main/java/xiaozhi/modules/security/config/ShiroConfig.java
main/manager-api/src/main/java/xiaozhi/modules/security/service/PermissionInitService.java
删除: main/manager-api/src/main/java/xiaozhi/modules/security/config/TenantConfig.java
```

## 4. 启动步骤

### 4.1 环境准备
1. 确保Java 17+已安装并配置
2. 确保MySQL数据库已启动
3. 确保Redis服务已启动
4. 确保数据库`xiaozhi_esp32_server`已创建

### 4.2 数据库初始化
```sql
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS xiaozhi_esp32_server 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE xiaozhi_esp32_server;
```

### 4.3 启动应用
```bash
cd main/manager-api
mvn clean compile
mvn spring-boot:run
```

## 5. 验证权限框架

启动成功后，可以通过以下接口验证权限框架：

### 5.1 基础接口
```bash
# 获取当前用户权限
GET http://localhost:8002/xiaozhi/sys/permission/user/permissions

# 权限校验测试
GET http://localhost:8002/xiaozhi/test/permission/public

# Service方法演示
GET http://localhost:8002/xiaozhi/demo/service/methods/summary
```

### 5.2 权限测试接口
```bash
# 平台管理员权限测试
GET http://localhost:8002/xiaozhi/test/permission/platform/admin

# 租户管理员权限测试  
GET http://localhost:8002/xiaozhi/test/permission/tenant/admin

# 设备管理权限测试
GET http://localhost:8002/xiaozhi/test/permission/device/manager
```

## 6. 常见问题

### 6.1 Java版本检查
```bash
# 检查Java版本
java -version

# 检查Maven使用的Java版本
mvn -version

# 检查JAVA_HOME
echo %JAVA_HOME%
```

### 6.2 数据库连接问题
检查`application-dev.yml`中的数据库配置：
```yaml
spring:
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: **************************************************************************************************************************************************
      username: wordpress
      password: wordpress
```

### 6.3 Redis连接问题
检查Redis服务是否启动：
```bash
# Windows
redis-server

# 或者检查Redis服务状态
redis-cli ping
```

## 7. 总结

权限框架的代码实现是正确的，主要问题是：

1. ✅ **Java版本不兼容** - 需要升级到Java 17+
2. ✅ **MyBatis配置冲突** - 已修复
3. ✅ **Shiro配置问题** - 已修复
4. ✅ **循环依赖问题** - 已修复

解决Java版本问题后，权限框架应该能够正常启动和运行。
