<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小智管理系统测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
</head>
<body>
    <div id="app">
        <el-container>
            <el-header style="background-color: #409EFF; color: white; text-align: center; line-height: 60px;">
                <h2>小智管理系统 - 权限修复测试</h2>
            </el-header>
            <el-main>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-card header="系统状态">
                            <p>✅ Vue.js 加载成功</p>
                            <p>✅ Element UI 加载成功</p>
                            <p>✅ 基础组件渲染正常</p>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card header="功能测试">
                            <el-button type="primary" @click="testMessage">测试消息</el-button>
                            <el-button type="success" @click="testDialog">测试对话框</el-button>
                            <el-button type="warning" @click="testNotification">测试通知</el-button>
                        </el-card>
                    </el-col>
                </el-row>
                
                <el-row style="margin-top: 20px;">
                    <el-col :span="24">
                        <el-card header="权限系统状态">
                            <el-alert 
                                title="权限系统已临时禁用" 
                                type="warning" 
                                description="为了确保应用正常启动，权限控制功能已临时禁用。所有功能按钮都将显示。"
                                show-icon>
                            </el-alert>
                            <div style="margin-top: 15px;">
                                <el-button type="primary">创建企业</el-button>
                                <el-button type="primary">创建租户</el-button>
                                <el-button type="primary">创建用户</el-button>
                                <el-button type="primary">权限管理</el-button>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
                
                <el-row style="margin-top: 20px;">
                    <el-col :span="24">
                        <el-card header="下一步操作">
                            <ol>
                                <li>确认基础功能正常后，逐步启用权限系统</li>
                                <li>测试权限指令和混入功能</li>
                                <li>验证权限API调用</li>
                                <li>完成权限系统集成</li>
                            </ol>
                            <div style="margin-top: 15px;">
                                <el-button type="primary" onclick="window.open('http://localhost:8001', '_blank')">
                                    打开完整应用测试
                                </el-button>
                                <el-button type="success" onclick="window.open('http://localhost:8001/test', '_blank')">
                                    打开HeaderBar测试页面
                                </el-button>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-main>
        </el-container>
    </div>

    <script>
        new Vue({
            el: '#app',
            methods: {
                testMessage() {
                    this.$message.success('消息功能正常！');
                },
                testDialog() {
                    this.$alert('对话框功能正常！', '测试', {
                        confirmButtonText: '确定'
                    });
                },
                testNotification() {
                    this.$notify({
                        title: '测试',
                        message: '通知功能正常！',
                        type: 'success'
                    });
                }
            }
        });
    </script>
</body>
</html>
