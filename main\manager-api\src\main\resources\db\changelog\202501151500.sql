-- 多租户改造数据库脚本 - 6.2阶段
-- 版本: 202501151500
-- 作者: AI Assistant
-- 描述: 实现多租户架构的数据库改造，包括企业、租户、用户、智能体、设备的租户隔离

-- =====================================================
-- 1. 企业管理表
-- =====================================================
CREATE TABLE `sys_organization` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '企业ID',
    `org_code` VARCHAR(50) NOT NULL COMMENT '企业编码',
    `org_name` VARCHAR(100) NOT NULL COMMENT '企业名称',
    `org_type` TINYINT DEFAULT 1 COMMENT '企业类型：1-普通企业，2-集团企业',
    `contact_person` VARCHAR(50) COMMENT '联系人',
    `contact_phone` VARCHAR(20) COMMENT '联系电话',
    `contact_email` VARCHAR(100) COMMENT '联系邮箱',
    `address` VARCHAR(200) COMMENT '企业地址',
    `business_license` VARCHAR(100) COMMENT '营业执照号',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `expire_date` DATETIME COMMENT '到期时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `creator` BIGINT COMMENT '创建者',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_org_code` (`org_code`),
    KEY `idx_org_status` (`status`),
    KEY `idx_org_expire` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业管理表';

-- =====================================================
-- 2. 租户管理表
-- =====================================================
CREATE TABLE `sys_tenant` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '租户ID',
    `tenant_code` VARCHAR(50) NOT NULL COMMENT '租户编码',
    `tenant_name` VARCHAR(100) NOT NULL COMMENT '租户名称',
    `org_id` BIGINT NOT NULL COMMENT '关联企业ID',
    `admin_user_id` BIGINT COMMENT '租户管理员用户ID',
    `max_users` INT DEFAULT 10 COMMENT '最大用户数',
    `max_devices` INT DEFAULT 100 COMMENT '最大设备数',
    `max_agents` INT DEFAULT 5 COMMENT '最大智能体数',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `expire_date` DATETIME COMMENT '到期时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `creator` BIGINT COMMENT '创建者',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_code`),
    KEY `idx_tenant_org` (`org_id`),
    KEY `idx_tenant_status` (`status`),
    KEY `idx_tenant_expire` (`expire_date`),
    CONSTRAINT `fk_tenant_org` FOREIGN KEY (`org_id`) REFERENCES `sys_organization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户管理表';

-- =====================================================
-- 3. 用户表增加租户字段
-- =====================================================
ALTER TABLE `sys_user` 
ADD COLUMN `tenant_id` BIGINT COMMENT '租户ID',
ADD COLUMN `user_type` TINYINT DEFAULT 3 COMMENT '用户类型：1-平台管理员，2-租户管理员，3-普通用户',
ADD COLUMN `real_name` VARCHAR(50) COMMENT '真实姓名',
ADD COLUMN `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除';

-- 为用户表添加索引
ALTER TABLE `sys_user` 
ADD KEY `idx_user_tenant` (`tenant_id`),
ADD KEY `idx_user_type` (`user_type`),
ADD KEY `idx_user_deleted` (`deleted`);

-- =====================================================
-- 4. 智能体表增加租户字段
-- =====================================================
ALTER TABLE `ai_agent` 
ADD COLUMN `tenant_id` BIGINT COMMENT '租户ID',
ADD COLUMN `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除';

-- 为智能体表添加索引
ALTER TABLE `ai_agent` 
ADD KEY `idx_agent_tenant` (`tenant_id`),
ADD KEY `idx_agent_deleted` (`deleted`);

-- =====================================================
-- 5. 设备表增加租户字段
-- =====================================================
ALTER TABLE `ai_device` 
ADD COLUMN `tenant_id` BIGINT COMMENT '租户ID',
ADD COLUMN `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除';

-- 为设备表添加索引
ALTER TABLE `ai_device` 
ADD KEY `idx_device_tenant` (`tenant_id`),
ADD KEY `idx_device_deleted` (`deleted`);

-- =====================================================
-- 6. 智能体分配管理表
-- =====================================================
CREATE TABLE `ai_agent_assignment` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '分配ID',
    `agent_id` VARCHAR(50) NOT NULL COMMENT '智能体ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `status` INT DEFAULT 1 COMMENT '分配状态（0:未分配 1:已分配）',
    `assigned_at` TIMESTAMP COMMENT '分配时间',
    `unassigned_at` TIMESTAMP COMMENT '取消分配时间',
    `assigned_by` BIGINT COMMENT '分配者ID',
    `unassigned_by` BIGINT COMMENT '取消分配者ID',
    `remark` VARCHAR(500) COMMENT '备注',
    `creator` BIGINT COMMENT '创建者',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_assignment_agent` (`agent_id`),
    KEY `idx_assignment_user` (`user_id`),
    KEY `idx_assignment_tenant` (`tenant_id`),
    KEY `idx_assignment_status` (`status`),
    UNIQUE KEY `uk_agent_user` (`agent_id`, `user_id`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能体分配管理表';

-- =====================================================
-- 7. 智能体使用统计表
-- =====================================================
CREATE TABLE `ai_agent_usage_statistics` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '统计ID',
    `agent_id` VARCHAR(50) NOT NULL COMMENT '智能体ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `statistics_date` DATE COMMENT '统计日期',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `total_duration` BIGINT DEFAULT 0 COMMENT '总使用时长（秒）',
    `avg_duration` BIGINT DEFAULT 0 COMMENT '平均使用时长（秒）',
    `max_duration` BIGINT DEFAULT 0 COMMENT '最长使用时长（秒）',
    `min_duration` BIGINT DEFAULT 0 COMMENT '最短使用时长（秒）',
    `success_count` INT DEFAULT 0 COMMENT '成功次数',
    `failure_count` INT DEFAULT 0 COMMENT '失败次数',
    `error_rate` DOUBLE DEFAULT 0 COMMENT '错误率',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_agent_stats_agent` (`agent_id`),
    KEY `idx_agent_stats_user` (`user_id`),
    KEY `idx_agent_stats_tenant` (`tenant_id`),
    KEY `idx_agent_stats_date` (`statistics_date`),
    UNIQUE KEY `uk_agent_user_date` (`agent_id`, `user_id`, `statistics_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能体使用统计表';

-- =====================================================
-- 8. 设备激活记录表
-- =====================================================
CREATE TABLE `ai_device_activation` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '激活记录ID',
    `device_id` VARCHAR(50) COMMENT '设备ID',
    `mac_address` VARCHAR(50) NOT NULL COMMENT '设备MAC地址',
    `activation_code` VARCHAR(20) NOT NULL COMMENT '激活码',
    `agent_id` VARCHAR(50) NOT NULL COMMENT '智能体ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `activation_status` INT DEFAULT 0 COMMENT '激活状态（0:未激活 1:已激活 2:激活失败）',
    `activated_at` TIMESTAMP COMMENT '激活时间',
    `code_generated_at` TIMESTAMP COMMENT '激活码生成时间',
    `code_expired_at` TIMESTAMP COMMENT '激活码过期时间',
    `activation_ip` VARCHAR(50) COMMENT '激活IP地址',
    `board` VARCHAR(50) COMMENT '设备硬件型号',
    `app_version` VARCHAR(20) COMMENT '固件版本号',
    `failure_reason` VARCHAR(200) COMMENT '激活失败原因',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `creator` BIGINT COMMENT '创建者',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_activation_code` (`activation_code`),
    KEY `idx_activation_mac` (`mac_address`),
    KEY `idx_activation_device` (`device_id`),
    KEY `idx_activation_user` (`user_id`),
    KEY `idx_activation_tenant` (`tenant_id`),
    KEY `idx_activation_status` (`activation_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备激活记录表';

-- =====================================================
-- 9. 设备使用统计表
-- =====================================================
CREATE TABLE `ai_device_usage_statistics` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '统计ID',
    `device_id` VARCHAR(50) NOT NULL COMMENT '设备ID',
    `mac_address` VARCHAR(50) NOT NULL COMMENT '设备MAC地址',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `agent_id` VARCHAR(50) COMMENT '智能体ID',
    `statistics_date` DATE COMMENT '统计日期',
    `connection_count` INT DEFAULT 0 COMMENT '连接次数',
    `total_online_duration` BIGINT DEFAULT 0 COMMENT '总在线时长（秒）',
    `avg_online_duration` BIGINT DEFAULT 0 COMMENT '平均在线时长（秒）',
    `max_online_duration` BIGINT DEFAULT 0 COMMENT '最长在线时长（秒）',
    `min_online_duration` BIGINT DEFAULT 0 COMMENT '最短在线时长（秒）',
    `success_connection_count` INT DEFAULT 0 COMMENT '成功连接次数',
    `failure_connection_count` INT DEFAULT 0 COMMENT '失败连接次数',
    `connection_success_rate` DOUBLE DEFAULT 0 COMMENT '连接成功率',
    `data_transfer_bytes` BIGINT DEFAULT 0 COMMENT '数据传输量（字节）',
    `last_connected_at` TIMESTAMP COMMENT '最后连接时间',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_device_stats_device` (`device_id`),
    KEY `idx_device_stats_mac` (`mac_address`),
    KEY `idx_device_stats_user` (`user_id`),
    KEY `idx_device_stats_tenant` (`tenant_id`),
    KEY `idx_device_stats_agent` (`agent_id`),
    KEY `idx_device_stats_date` (`statistics_date`),
    UNIQUE KEY `uk_device_user_date` (`device_id`, `user_id`, `statistics_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备使用统计表';
