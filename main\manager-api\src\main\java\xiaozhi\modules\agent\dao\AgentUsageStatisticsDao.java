package xiaozhi.modules.agent.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import xiaozhi.common.dao.BaseDao;
import xiaozhi.modules.agent.entity.AgentUsageStatisticsEntity;

/**
 * 智能体使用统计DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Mapper
public interface AgentUsageStatisticsDao extends BaseDao<AgentUsageStatisticsEntity> {
    
    /**
     * 根据智能体ID和日期范围获取统计数据
     * 
     * @param agentId 智能体ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<AgentUsageStatisticsEntity> getStatisticsByAgentAndDateRange(
        @Param("agentId") String agentId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate
    );
    
    /**
     * 根据用户ID和日期范围获取统计数据
     * 
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<AgentUsageStatisticsEntity> getStatisticsByUserAndDateRange(
        @Param("userId") Long userId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate
    );
    
    /**
     * 获取智能体总体统计信息
     * 
     * @param agentId 智能体ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Map<String, Object> getAgentOverallStatistics(
        @Param("agentId") String agentId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate
    );
    
    /**
     * 获取热门智能体排行
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行列表
     */
    List<Map<String, Object>> getPopularAgentsRanking(
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate,
        @Param("limit") Integer limit
    );
    
    /**
     * 获取租户智能体使用统计
     * 
     * @param tenantId 租户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Map<String, Object> getTenantAgentStatistics(
        @Param("tenantId") Long tenantId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate
    );
    
    /**
     * 记录智能体使用情况
     * 
     * @param agentId 智能体ID
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param duration 使用时长
     * @param isSuccess 是否成功
     */
    void recordAgentUsage(
        @Param("agentId") String agentId,
        @Param("userId") Long userId,
        @Param("tenantId") Long tenantId,
        @Param("duration") Long duration,
        @Param("isSuccess") Boolean isSuccess
    );
}
