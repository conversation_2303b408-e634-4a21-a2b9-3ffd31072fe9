<template>
  <div class="permission-management">
    <HeaderBar />
    
    <div class="main-content">
      <div class="content-header">
        <h2>权限管理</h2>
      </div>

      <!-- 功能选项卡 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="用户权限" name="user-permission">
          <!-- 用户权限管理 -->
          <div class="tab-content">
            <!-- 搜索区域 -->
            <div class="search-area">
              <el-form :inline="true" :model="userSearchForm" class="search-form">
                <el-form-item label="用户名">
                  <el-input v-model="userSearchForm.username" placeholder="请输入用户名" clearable />
                </el-form-item>
                <el-form-item label="真实姓名">
                  <el-input v-model="userSearchForm.realName" placeholder="请输入真实姓名" clearable />
                </el-form-item>
                <el-form-item label="用户类型">
                  <el-select v-model="userSearchForm.userType" placeholder="请选择用户类型" clearable>
                    <el-option label="平台管理员" :value="1" />
                    <el-option label="租户管理员" :value="2" />
                    <el-option label="普通用户" :value="3" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleUserSearch">搜索</el-button>
                  <el-button @click="handleUserReset">重置</el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 用户列表 -->
            <div class="table-area">
              <el-table 
                :data="userList" 
                v-loading="userLoading"
                stripe
                border
                style="width: 100%"
              >
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="realName" label="真实姓名" width="120" />
                <el-table-column prop="userTypeName" label="用户类型" width="120" />
                <el-table-column prop="mobile" label="手机号" width="130" />
                <el-table-column prop="email" label="邮箱" min-width="180" />
                <el-table-column prop="statusName" label="状态" width="80">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                      {{ scope.row.statusName }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="handleViewUserPermissions(scope.row)">查看权限</el-button>
                    <el-button size="mini" type="primary" @click="handleManageUserRoles(scope.row)">管理角色</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="pagination-area">
                <el-pagination
                  @size-change="handleUserSizeChange"
                  @current-change="handleUserCurrentChange"
                  :current-page="userCurrentPage"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="userPageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="userTotal"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="权限分析" name="permission-analysis">
          <!-- 权限分析 -->
          <div class="tab-content">
            <div class="analysis-area">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="stat-card">
                    <div class="stat-title">总用户数</div>
                    <div class="stat-value">{{ analysisData.totalUsers || 0 }}</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-card">
                    <div class="stat-title">活跃用户数</div>
                    <div class="stat-value">{{ analysisData.activeUsers || 0 }}</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-card">
                    <div class="stat-title">权限总数</div>
                    <div class="stat-value">{{ analysisData.totalPermissions || 0 }}</div>
                  </div>
                </el-col>
              </el-row>

              <div class="permission-list-area">
                <h3>系统权限列表</h3>
                <el-table :data="availablePermissions" stripe border>
                  <el-table-column prop="permission" label="权限代码" min-width="200" />
                  <el-table-column prop="description" label="权限描述" min-width="200" />
                  <el-table-column prop="domain" label="权限域" width="120" />
                  <el-table-column prop="action" label="操作" width="120" />
                  <el-table-column prop="scope" label="范围" width="120" />
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 用户权限查看对话框 -->
    <el-dialog title="用户权限详情" :visible.sync="showUserPermissionDialog" width="800px">
      <div v-if="currentUser">
        <div class="user-info">
          <h4>用户信息</h4>
          <p><strong>用户名：</strong>{{ currentUser.username }}</p>
          <p><strong>真实姓名：</strong>{{ currentUser.realName }}</p>
          <p><strong>用户类型：</strong>{{ currentUser.userTypeName }}</p>
        </div>
        
        <div class="permission-info">
          <h4>权限列表</h4>
          <el-table :data="currentUserPermissions" stripe border max-height="400">
            <el-table-column prop="permission" label="权限代码" min-width="200" />
            <el-table-column prop="description" label="权限描述" min-width="200" />
            <el-table-column prop="source" label="权限来源" width="120" />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 用户角色管理对话框 -->
    <el-dialog title="管理用户角色" :visible.sync="showUserRoleDialog" width="700px">
      <div v-if="currentUser">
        <div class="user-info">
          <h4>用户信息</h4>
          <p><strong>用户名：</strong>{{ currentUser.username }}</p>
          <p><strong>真实姓名：</strong>{{ currentUser.realName }}</p>
        </div>

        <div class="role-management">
          <h4>角色分配</h4>
          <div class="role-section">
            <div class="current-roles">
              <h5>当前角色</h5>
              <div class="role-tags">
                <el-tag 
                  v-for="role in currentUserRoles" 
                  :key="role.id"
                  closable
                  @close="handleRemoveRole(role)"
                  style="margin: 5px;"
                >
                  {{ role.roleName }}
                </el-tag>
              </div>
            </div>

            <div class="available-roles">
              <h5>可分配角色</h5>
              <el-select 
                v-model="selectedRoleIds" 
                multiple 
                placeholder="请选择角色"
                style="width: 100%"
              >
                <el-option 
                  v-for="role in availableRoles" 
                  :key="role.id" 
                  :label="role.roleName" 
                  :value="role.id"
                  :disabled="currentUserRoles.some(r => r.id === role.id)"
                />
              </el-select>
              <el-button 
                type="primary" 
                @click="handleAssignRoles" 
                :disabled="selectedRoleIds.length === 0"
                style="margin-top: 10px;"
              >
                分配角色
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <VersionFooter />
  </div>
</template>

<script>
import HeaderBar from "@/components/HeaderBar.vue";
import VersionFooter from "@/components/VersionFooter.vue";
import userApi from "@/apis/module/user";
import permissionApi from "@/apis/module/permission";

export default {
  name: "PermissionManagement",
  components: {
    HeaderBar,
    VersionFooter
  },
  data() {
    return {
      activeTab: 'user-permission',
      
      // 用户权限管理
      userSearchForm: {
        username: '',
        realName: '',
        userType: null
      },
      userList: [],
      userLoading: false,
      userCurrentPage: 1,
      userPageSize: 10,
      userTotal: 0,

      // 权限分析
      analysisData: {},
      availablePermissions: [],

      // 对话框
      showUserPermissionDialog: false,
      showUserRoleDialog: false,
      currentUser: null,
      currentUserPermissions: [],
      currentUserRoles: [],
      availableRoles: [],
      selectedRoleIds: []
    };
  },
  created() {
    this.fetchUsers();
    this.fetchPermissionAnalysis();
    this.fetchAvailablePermissions();
    this.fetchAvailableRoles();
  },
  methods: {
    // 标签页切换
    handleTabClick(tab) {
      if (tab.name === 'permission-analysis') {
        this.fetchPermissionAnalysis();
      }
    },

    // 获取用户列表
    fetchUsers() {
      this.userLoading = true;
      const params = {
        page: this.userCurrentPage,
        limit: this.userPageSize,
        ...this.userSearchForm
      };
      
      userApi.getUserPage(params, (res) => {
        this.userLoading = false;
        if (res.code === 0) {
          this.userList = res.data.list || [];
          this.userTotal = res.data.total || 0;
        } else {
          this.$message.error(res.msg || '获取用户列表失败');
        }
      });
    },

    // 用户搜索
    handleUserSearch() {
      this.userCurrentPage = 1;
      this.fetchUsers();
    },

    // 重置用户搜索
    handleUserReset() {
      this.userSearchForm = {
        username: '',
        realName: '',
        userType: null
      };
      this.userCurrentPage = 1;
      this.fetchUsers();
    },

    // 用户分页大小改变
    handleUserSizeChange(val) {
      this.userPageSize = val;
      this.userCurrentPage = 1;
      this.fetchUsers();
    },

    // 用户当前页改变
    handleUserCurrentChange(val) {
      this.userCurrentPage = val;
      this.fetchUsers();
    },

    // 查看用户权限
    handleViewUserPermissions(user) {
      this.currentUser = user;
      permissionApi.getUserPermissions(user.id, (res) => {
        if (res.code === 0) {
          this.currentUserPermissions = res.data || [];
          this.showUserPermissionDialog = true;
        } else {
          this.$message.error(res.msg || '获取用户权限失败');
        }
      });
    },

    // 管理用户角色
    handleManageUserRoles(user) {
      this.currentUser = user;
      this.selectedRoleIds = [];
      
      // 获取用户当前角色
      permissionApi.getUserRoles(user.id, (res) => {
        if (res.code === 0) {
          this.currentUserRoles = res.data || [];
          this.showUserRoleDialog = true;
        } else {
          this.$message.error(res.msg || '获取用户角色失败');
        }
      });
    },

    // 分配角色
    handleAssignRoles() {
      if (this.selectedRoleIds.length === 0) {
        this.$message.warning('请选择要分配的角色');
        return;
      }

      const data = {
        userIds: [this.currentUser.id],
        roleIds: this.selectedRoleIds
      };

      permissionApi.assignRole(data, (res) => {
        if (res.code === 0) {
          this.$message.success('角色分配成功');
          this.selectedRoleIds = [];
          // 重新获取用户角色
          this.handleManageUserRoles(this.currentUser);
        } else {
          this.$message.error(res.msg || '角色分配失败');
        }
      });
    },

    // 移除角色
    handleRemoveRole(role) {
      this.$confirm(`确定要移除角色"${role.roleName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        permissionApi.removeRole(this.currentUser.id, role.id, (res) => {
          if (res.code === 0) {
            this.$message.success('角色移除成功');
            // 重新获取用户角色
            this.handleManageUserRoles(this.currentUser);
          } else {
            this.$message.error(res.msg || '角色移除失败');
          }
        });
      });
    },

    // 获取权限分析数据
    fetchPermissionAnalysis() {
      permissionApi.getPermissionAnalysis((res) => {
        if (res.code === 0) {
          this.analysisData = res.data || {};
        }
      });
    },

    // 获取可用权限列表
    fetchAvailablePermissions() {
      permissionApi.getAvailablePermissions((res) => {
        if (res.code === 0) {
          this.availablePermissions = res.data || [];
        }
      });
    },

    // 获取可分配角色
    fetchAvailableRoles() {
      permissionApi.getAvailableRoles((res) => {
        if (res.code === 0) {
          this.availableRoles = res.data || [];
        }
      });
    }
  }
};
</script>

<style scoped>
.permission-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.content-header {
  margin-bottom: 20px;
}

.content-header h2 {
  margin: 0;
  color: #333;
}

.tab-content {
  margin-top: 20px;
}

.search-area {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-area {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-area {
  margin-top: 20px;
  text-align: right;
}

.analysis-area {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.permission-list-area {
  margin-top: 30px;
}

.permission-list-area h3 {
  margin-bottom: 15px;
  color: #333;
}

.user-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.user-info h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.user-info p {
  margin: 5px 0;
  color: #666;
}

.permission-info h4,
.role-management h4 {
  margin: 20px 0 10px 0;
  color: #333;
}

.role-section {
  margin-top: 15px;
}

.current-roles,
.available-roles {
  margin-bottom: 20px;
}

.current-roles h5,
.available-roles h5 {
  margin: 0 0 10px 0;
  color: #666;
}

.role-tags {
  min-height: 40px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}
</style>
