# 前端调整指导文档

## 1. 概述

由于后端多租户改造的持续进行，前端需要进行相应的调整以适配新的API接口和数据结构。本文档详细说明了已完成改造和即将进行改造对前端的影响，指导前端开发人员进行相应调整。

### 1.1 改造进度
- ✅ **已完成**: 用户管理改造、企业租户管理、智能体管理改造、设备管理改造
- 🚧 **进行中**: 前端界面改造（6.3）
- ⏳ **待进行**: 系统集成测试、生产部署

## 2. 已完成的后端改造影响

### 2.1 用户管理改造影响

#### 用户实体变更
- ✅ 新增 `tenantId` 字段 - 租户ID（后端自动设置）
- ✅ 新增 `userType` 字段 - 用户类型（1:平台管理员 2:租户管理员 3:普通用户）
- ✅ 完善 `realName` 字段 - 真实姓名

#### 前端需要调整的内容
```javascript
// 用户查询参数增加
const queryParams = {
  username: '',
  realName: '',
  userType: null,    // 新增：用户类型筛选
  status: null,
  // tenantId 由后端自动处理，前端无需传递
}

// 用户表单字段增加
const userForm = {
  username: '',
  realName: '',
  password: '',
  userType: 2,       // 新增：用户类型，默认为租户用户
  deptId: null,
  status: 1,
  // tenantId 由后端自动设置
}
```

### 2.2 企业租户管理新增功能

#### 新增API接口
**企业管理API**:
```javascript
// 企业分页查询
POST /sys/organization/page
// 企业创建
POST /sys/organization
// 企业更新
PUT /sys/organization
// 企业删除
DELETE /sys/organization/{id}
// 企业详情
GET /sys/organization/detail/{id}
// 企业统计
GET /sys/organization/{id}/statistics
```

**租户管理API**:
```javascript
// 租户分页查询
POST /sys/tenant/page
// 租户创建
POST /sys/tenant
// 租户更新
PUT /sys/tenant
// 租户删除
DELETE /sys/tenant/{id}
// 租户详情
GET /sys/tenant/detail/{id}
// 租户统计
GET /sys/tenant/{id}/statistics
```

#### 前端需要新增的界面
1. **企业管理界面** - 完整的企业CRUD操作
2. **租户管理界面** - 完整的租户CRUD操作
3. **配额管理界面** - 租户资源配额设置和监控
4. **到期管理界面** - 企业/租户到期提醒和管理

## 3. 已完成的智能体管理改造影响

### 3.1 智能体租户隔离（已完成）

#### 后端变更
- ✅ 智能体实体已增加 `tenantId` 字段
- ✅ 智能体查询自动按租户过滤
- ✅ 智能体权限控制基于租户隔离

#### 前端需要调整的内容
```javascript
// 智能体查询 - 后端已自动按租户过滤
const getAgentList = async () => {
  const response = await api.get('/agent/list');
  // 后端自动按租户过滤，前端无需额外处理
  return response.data;
}

// 智能体创建表单
const agentForm = {
  agentName: '',
  systemPrompt: '',
  // tenantId 由后端自动设置，前端无需处理
}
```

### 3.2 智能体权限控制（已完成）

#### 权限控制逻辑
```javascript
// 根据用户类型控制智能体操作权限
const userType = getCurrentUserType();

const agentPermissions = {
  canViewAll: userType === 1,        // 平台管理员可查看所有
  canCreate: userType <= 2,          // 平台管理员和租户管理员可创建
  canEdit: userType <= 2,            // 平台管理员和租户管理员可编辑
  canDelete: userType <= 2,          // 平台管理员和租户管理员可删除
  canAssign: userType <= 2,          // 平台管理员和租户管理员可分配
}
```

### 3.3 智能体分配管理（已完成）

#### 新增API
```javascript
// 智能体分配
POST /agent/assignment/assign
{
  "agentIds": ["agent1", "agent2"],
  "userIds": [1, 2, 3],
  "remark": "批量分配智能体"
}

// 取消分配
DELETE /agent/assignment/unassign
{
  "agentIds": ["agent1"],
  "userIds": [1]
}

// 分配记录查询
POST /agent/assignment/page
{
  "page": 1,
  "limit": 10,
  "agentId": "agent1",
  "userId": 1,
  "status": 1
}

// 获取用户已分配智能体
GET /agent/assignment/user/{userId}/agents

// 获取智能体已分配用户
GET /agent/assignment/agent/{agentId}/users
```

#### 前端需要新增的界面
- **智能体分配界面** - 智能体与用户的分配管理
- **分配记录界面** - 分配历史和状态查询

### 3.4 智能体使用统计（已完成）

#### 新增API
```javascript
// 智能体使用统计
POST /agent/statistics/query
{
  "agentId": "agent1",
  "startDate": "2025-01-01",
  "endDate": "2025-01-31",
  "statisticsType": "daily"
}

// 热门智能体排行
GET /agent/statistics/popular?startDate=2025-01-01&endDate=2025-01-31&limit=10

// 记录智能体使用
POST /agent/statistics/usage?agentId=agent1&userId=1&duration=60&isSuccess=true

// 租户智能体概览
GET /agent/statistics/tenant/overview

// 指定租户智能体概览（平台管理员）
GET /agent/statistics/tenant/{tenantId}/overview
```

#### 前端需要新增的界面
- **智能体统计界面** - 使用统计和分析
- **使用趋势图表** - 可视化使用趋势
- **热门排行榜** - 智能体使用排行
- **租户概览界面** - 租户级别的智能体统计

## 4. 已完成的设备管理改造影响

### 4.1 设备租户隔离（已完成）

#### 后端变更
- ✅ 设备实体已增加 `tenantId` 字段
- ✅ 设备查询自动按租户过滤
- ✅ 设备权限控制基于租户隔离

#### 前端需要调整的内容
```javascript
// 设备查询 - 后端已自动按租户过滤
const getDeviceList = async (agentId) => {
  const response = await api.get(`/device/bind/${agentId}`);
  // 后端自动按租户过滤，前端无需额外处理
  return response.data;
}

// 设备绑定 - 后端自动设置租户ID
const bindDevice = async (agentId, deviceCode) => {
  const response = await api.post(`/device/bind/${agentId}/${deviceCode}`);
  // tenantId 由后端自动设置，前端无需处理
  return response.data;
}
```

### 4.2 设备权限控制（已完成）

#### 权限控制逻辑
```javascript
// 根据用户类型控制设备操作权限
const userType = getCurrentUserType();

const devicePermissions = {
  canViewAll: userType === 1,        // 平台管理员可查看所有
  canBind: userType <= 3,            // 所有用户都可以绑定设备
  canUnbind: userType <= 3,          // 所有用户都可以解绑自己的设备
  canManage: userType <= 2,          // 平台管理员和租户管理员可管理
  canActivate: userType <= 2,        // 平台管理员和租户管理员可激活
}
```

### 4.3 设备激活流程（已完成）

#### 新增API
```javascript
// 设备激活管理
POST /device/activation/page
{
  "page": 1,
  "limit": 10,
  "deviceId": "device123",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "activationStatus": 1
}

// 创建激活记录
POST /device/activation/create?macAddress=AA:BB:CC:DD:EE:FF&agentId=agent123&activationCode=123456

// 更新激活状态
POST /device/activation/update-status?activationCode=123456&status=1&deviceId=device123

// 根据激活码查询
GET /device/activation/code/{activationCode}

// 根据MAC地址查询
GET /device/activation/mac/{macAddress}
```

#### 前端需要新增的界面
- **设备激活管理界面** - 激活记录的CRUD操作
- **激活状态监控界面** - 激活状态实时监控

### 4.4 设备使用统计（已完成）

#### 新增API
```javascript
// 设备使用统计
POST /device/statistics/query
{
  "deviceId": "device123",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "startDate": "2025-01-01",
  "endDate": "2025-01-31",
  "statisticsType": "daily"
}

// 活跃设备排行
GET /device/statistics/active?startDate=2025-01-01&endDate=2025-01-31&limit=10

// 记录设备使用
POST /device/statistics/usage?deviceId=device123&macAddress=AA:BB:CC:DD:EE:FF&onlineDuration=3600&isSuccess=true&dataTransferBytes=1048576

// 租户设备概览
GET /device/statistics/tenant/overview

// 指定租户设备概览（平台管理员）
GET /device/statistics/tenant/{tenantId}/overview
```

#### 前端需要新增的界面
- **设备统计界面** - 使用统计和分析
- **设备使用趋势图表** - 可视化使用趋势
- **活跃设备排行榜** - 设备使用排行
- **租户设备概览界面** - 租户级别的设备统计

## 5. API路径变更（用户管理相关）

### 2.1 认证相关接口

**原路径**: `/user/*`  
**新路径**: `/auth/*`

| 原接口 | 新接口 | 说明 |
|--------|--------|------|
| `POST /user/login` | `POST /auth/login` | 统一登录接口 |
| `POST /user/register` | `POST /auth/register` | 普通用户注册 |
| `GET /user/captcha` | `GET /auth/captcha` | 验证码获取 |
| `POST /user/smsVerification` | `POST /auth/smsVerification` | 短信验证码 |
| `GET /user/info` | `GET /auth/info` | 用户信息获取 |
| `PUT /user/change-password` | `PUT /auth/change-password` | 修改密码 |
| `PUT /user/retrieve-password` | `PUT /auth/retrieve-password` | 找回密码 |
| `GET /user/pub-config` | `GET /auth/pub-config` | 公共配置 |

### 2.2 用户管理接口

**原路径**: `/admin/*`  
**新路径**: `/user/*`

| 原接口 | 新接口 | 说明 |
|--------|--------|------|
| `GET /admin/users` | `GET /user` | 分页查询用户 |
| `PUT /admin/users/{id}` | `PUT /user/{id}/reset-password` | 重置密码 |
| `DELETE /admin/users/{id}` | `DELETE /user/{id}` | 删除用户 |
| `PUT /admin/users/changeStatus/{status}` | `PUT /user/{id}/status` | 修改用户状态 |

**新增接口**:
- `GET /user/list` - 获取用户列表
- `GET /user/{userId}` - 获取用户详情
- `POST /user` - 创建用户
- `PUT /user` - 更新用户
- `GET /user/{userId}/roles` - 获取用户角色
- `GET /user/{userId}/permissions` - 获取用户权限

### 2.3 角色权限接口

**原路径**: `/sys/user-role/*`  
**新路径**: `/user-role/*`

| 原接口 | 新接口 | 说明 |
|--------|--------|------|
| `GET /sys/user-role/user/{userId}/roles` | `GET /user-role/user/{userId}/roles` | 获取用户角色 |
| `GET /sys/user-role/role/{roleId}/users` | `GET /user-role/role/{roleId}/users` | 获取角色用户 |
| `POST /sys/user-role/assign` | `POST /user-role/assign` | 分配角色 |
| `DELETE /sys/user-role/user/{userId}/role/{roleId}` | `DELETE /user-role/{userId}/{roleId}` | 取消角色 |
| `GET /sys/user-role/tenant/available-roles` | `GET /user-role/available-roles` | 可分配角色 |

**原路径**: `/sys/user-permission/*`  
**新路径**: `/user-permission/*`

| 原接口 | 新接口 | 说明 |
|--------|--------|------|
| `GET /sys/user-permission/current/info` | `GET /user-permission/current/info` | 当前用户权限 |
| `GET /sys/user-permission/user/{userId}/permissions` | `GET /user-permission/user/{userId}/permissions` | 用户权限 |
| `GET /sys/user-permission/check/{userId}/{permission}` | `GET /user-permission/check/{userId}/{permission}` | 权限检查 |
| `GET /sys/user-permission/tenant/users/permissions` | `GET /user-permission/available-permissions` | 权限概览 |
| `GET /sys/user-permission/permission/analysis` | `GET /user-permission/permission/analysis` | 权限分析 |

## 3. 请求参数变更

### 3.1 登录请求

**原格式**:
```javascript
{
  username: "admin",
  password: "123456",
  captchaId: "uuid-1234",
  captcha: "abcd",
  mobileCaptcha: "123456"
}
```

**新格式**:
```javascript
{
  username: "admin",
  password: "123456",
  captchaId: "uuid-1234",
  captcha: "abcd",
  mobileCaptcha: "123456",
  rememberMe: false
}
```

### 3.2 注册请求

**原格式**:
```javascript
{
  username: "testuser",
  password: "123456",
  captchaId: "uuid-1234",
  captcha: "abcd"
}
```

**新格式**:
```javascript
{
  username: "testuser",
  password: "123456",
  confirmPassword: "123456",
  realName: "张三",
  mobile: "13800138000",
  email: "<EMAIL>",
  captchaId: "uuid-1234",
  captcha: "abcd",
  mobileCaptcha: "123456"
}
```

### 3.3 用户查询请求

**原格式** (使用Map参数):
```javascript
const params = {
  mobile: "13800138000",
  page: 1,
  limit: 10
}
```

**新格式** (使用DTO对象):
```javascript
const queryRequest = {
  page: 1,
  limit: 10,
  username: "admin",
  realName: "张三",
  mobile: "13800138000",
  email: "<EMAIL>",
  userType: 1,
  status: 1,
  deptId: 1,
  createStartDate: "2025-01-01",
  createEndDate: "2025-01-31",
  keywords: "张三"
}
```

### 3.4 用户创建请求

**新增格式**:
```javascript
const createRequest = {
  username: "testuser",
  password: "123456",
  realName: "张三",
  headUrl: "http://example.com/avatar.jpg",
  gender: 0,
  email: "<EMAIL>",
  mobile: "13800138000",
  userType: 2,
  status: 1,
  tenantId: 1,
  deptId: 1,
  roleIdList: [1, 2],
  postIdList: [1, 2]
}
```

## 4. 响应数据变更

### 4.1 登录响应

**原格式**:
```javascript
{
  code: 0,
  data: {
    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    expire: 7200
  }
}
```

**新格式**:
```javascript
{
  code: 0,
  data: {
    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    tokenType: "Bearer",
    expiresIn: 7200,
    userId: 1,
    username: "admin",
    realName: "管理员",
    userType: 1,
    tenantId: 1,
    tenantName: "测试租户",
    isPlatformAdmin: true,
    isTenantAdmin: false
  }
}
```

### 4.2 用户信息响应

**新格式**:
```javascript
{
  code: 0,
  data: {
    userId: 1,
    username: "admin",
    realName: "管理员",
    headUrl: "http://example.com/avatar.jpg",
    gender: 0,
    email: "<EMAIL>",
    mobile: "13800138000",
    userType: 1,
    status: 1,
    tenantId: 1,
    tenantName: "测试租户",
    deptId: 1,
    deptName: "技术部",
    createDate: "2025-01-15T10:00:00",
    roles: ["PLATFORM_ADMIN"],
    permissions: ["user:view:*", "user:create:*"],
    isPlatformAdmin: true,
    isTenantAdmin: false,
    isSuperAdmin: false
  }
}
```

### 4.3 用户详情响应

**新格式**:
```javascript
{
  code: 0,
  data: {
    id: 1,
    username: "admin",
    realName: "管理员",
    headUrl: "http://example.com/avatar.jpg",
    gender: 0,
    email: "<EMAIL>",
    mobile: "13800138000",
    userType: 1,
    userTypeName: "平台管理员",
    status: 1,
    statusName: "正常",
    tenantId: 1,
    tenantName: "测试租户",
    deptId: 1,
    deptName: "技术部",
    createDate: "2025-01-15T10:00:00",
    updateDate: "2025-01-15T10:00:00",
    roles: [
      {
        id: 1,
        roleCode: "PLATFORM_ADMIN",
        roleName: "平台管理员",
        roleType: 1
      }
    ],
    permissions: ["user:view:*", "user:create:*"],
    isPlatformAdmin: true,
    isTenantAdmin: false,
    isSuperAdmin: false
  }
}
```

## 5. 业务逻辑变更

### 5.1 登录流程简化

**原流程**:
1. 用户选择租户（如果有多个）
2. 输入用户名密码
3. 调用对应的登录接口

**新流程**:
1. 用户直接输入用户名密码
2. 调用统一登录接口
3. 后台自动识别用户租户

**前端调整**:
- 移除租户选择步骤
- 使用统一的登录接口
- 从登录响应中获取租户信息

### 5.2 用户管理统一化

**原逻辑**:
- 平台管理员使用 `/admin/*` 接口
- 租户管理员使用 `/sys/tenant/user/*` 接口

**新逻辑**:
- 所有用户管理都使用 `/user/*` 接口
- 后台根据用户权限自动过滤数据

**前端调整**:
- 统一使用 `/user/*` 接口
- 根据用户权限显示不同功能
- 移除租户相关的特殊处理

### 5.3 权限控制优化

**原逻辑**:
- 前端根据用户类型显示不同界面
- 手动处理租户数据过滤

**新逻辑**:
- 后台自动处理权限和数据过滤
- 前端根据权限响应显示功能

**前端调整**:
- 简化权限判断逻辑
- 依赖后台权限响应
- 统一错误处理

## 6. 文件调整清单

### 6.1 需要修改的API调用文件

```
src/apis/module/user.js - 用户相关API
src/apis/module/auth.js - 认证相关API（新建）
src/apis/module/admin.js - 管理员API（需要重构）
```

### 6.2 需要修改的页面组件

```
src/views/login/ - 登录页面
src/views/register/ - 注册页面
src/views/user/ - 用户管理页面
src/views/admin/ - 管理员页面（需要合并到用户管理）
src/components/UserInfo/ - 用户信息组件
```

### 6.3 需要修改的状态管理

```
src/store/modules/user.js - 用户状态管理
src/store/modules/auth.js - 认证状态管理
src/store/modules/permission.js - 权限状态管理
```

## 7. 迁移步骤建议

### 7.1 第一阶段：API适配
1. 创建新的API调用文件
2. 更新所有API路径
3. 调整请求参数格式
4. 适配响应数据结构

### 7.2 第二阶段：页面重构
1. 简化登录流程
2. 统一用户管理界面
3. 更新权限控制逻辑
4. 调整数据展示格式

### 7.3 第三阶段：测试验证
1. 功能测试
2. 权限测试
3. 兼容性测试
4. 性能测试

## 8. 注意事项

### 8.1 兼容性处理
- 保留旧API一段时间
- 提供数据格式转换
- 渐进式迁移

### 8.2 错误处理
- 统一错误码处理
- 权限错误提示
- 网络错误重试

### 8.3 用户体验
- 保持界面一致性
- 优化加载性能
- 提供操作反馈

## 9. 前端开发优先级

### 9.1 高优先级（立即需要）
1. **用户管理界面调整** - 适配新的用户类型字段
   - 用户列表增加用户类型筛选
   - 用户表单增加用户类型选择
   - 权限控制基于用户类型

2. **权限控制逻辑更新** - 根据用户类型控制界面显示
   ```javascript
   const USER_TYPES = {
     PLATFORM_ADMIN: 1,    // 平台管理员
     TENANT_ADMIN: 2,      // 租户管理员
     NORMAL_USER: 3        // 普通用户
   }
   ```

### 9.2 中优先级（近期需要）
1. **企业管理界面** - 新增完整的企业CRUD界面
2. **租户管理界面** - 新增完整的租户CRUD界面
3. **智能体分配界面** - 新增智能体分配管理界面
4. **智能体统计界面** - 新增智能体使用统计界面

### 9.3 低优先级（后期需要）
1. **设备激活管理界面** - 新增设备激活流程管理
2. **设备统计界面** - 新增设备使用统计和分析
3. **综合报表界面** - 多维度统计报表
4. **系统监控界面** - 系统运行状态监控

## 10. 租户透明化开发注意事项

### 10.1 核心原则
- **无需手动传递tenantId** - 所有API调用中，前端无需传递tenantId参数
- **自动数据过滤** - 后端会自动根据当前用户的租户进行数据过滤
- **权限自动控制** - 根据用户类型自动控制可访问的功能

### 10.2 权限检查函数
```javascript
// 用户类型常量
const USER_TYPES = {
  PLATFORM_ADMIN: 1,    // 平台管理员
  TENANT_ADMIN: 2,      // 租户管理员
  NORMAL_USER: 3        // 普通用户
}

// 权限检查函数
const hasPermission = (requiredUserType) => {
  const currentUserType = getCurrentUserType();
  return currentUserType <= requiredUserType;
}

// 使用示例
if (hasPermission(USER_TYPES.TENANT_ADMIN)) {
  // 显示管理功能
}
```

### 10.3 错误处理
```javascript
// 统一错误处理
const handleApiError = (error) => {
  if (error.code === 403) {
    // 权限不足
    ElMessage.error('权限不足，请联系管理员');
  } else if (error.code === 404) {
    // 资源不存在或不属于当前租户
    ElMessage.error('资源不存在或无权访问');
  }
}
```

## 11. 智能体管理改造准备

### 11.1 当前智能体管理现状
- 智能体与用户直接关联（userId字段）
- 无租户隔离机制
- 权限控制基于用户ID

### 11.2 改造后预期变化
- 智能体将增加租户隔离（tenantId字段）
- 权限控制基于租户和用户类型
- 支持智能体分配管理

### 11.3 前端准备工作
1. **代码审查** - 检查当前智能体相关代码
2. **接口梳理** - 整理智能体相关API调用
3. **权限逻辑** - 准备权限控制逻辑调整
4. **界面设计** - 设计新增的分配和统计界面

## 12. 文件调整清单更新

### 12.1 已完成调整的文件
```
✅ src/apis/module/auth.js - 认证相关API
✅ src/apis/module/user.js - 用户管理API
✅ src/apis/module/user-role.js - 用户角色API
✅ src/apis/module/user-permission.js - 用户权限API
```

### 12.2 需要新增的文件
```
🆕 src/apis/module/organization.js - 企业管理API
🆕 src/apis/module/tenant.js - 租户管理API
🆕 src/views/organization/ - 企业管理页面
🆕 src/views/tenant/ - 租户管理页面
```

### 12.3 已完成调整的文件（智能体改造）
```
✅ src/apis/module/agent.js - 智能体API（已适配租户隔离）
✅ src/views/agent/ - 智能体管理页面（已更新权限控制）
🆕 src/apis/module/agent-assignment.js - 智能体分配API
🆕 src/apis/module/agent-statistics.js - 智能体统计API
🆕 src/views/agent/assignment/ - 智能体分配页面
🆕 src/views/agent/statistics/ - 智能体统计页面
```

### 12.4 已完成调整的文件（设备改造）
```
✅ src/apis/module/device.js - 设备API（已适配租户隔离）
✅ src/views/device/ - 设备管理页面（已更新权限控制）
🆕 src/apis/module/device-activation.js - 设备激活API
🆕 src/apis/module/device-statistics.js - 设备统计API
🆕 src/views/device/activation/ - 设备激活管理页面
🆕 src/views/device/statistics/ - 设备统计页面
```

## 13. 总结

多租户改造是一个渐进式的过程，前端需要配合后端改造进度进行相应调整：

### 13.1 已完成的改造
- ✅ **用户管理优化** - 简化了用户管理复杂性，统一了API接口
- ✅ **企业租户管理** - 新增了完整的企业和租户管理功能
- ✅ **租户透明化** - 实现了对开发人员透明的租户处理

### 13.2 正在进行的改造
- 🚧 **智能体管理改造** - 即将开始，需要前端配合调整

### 13.3 后续改造计划
- ⏳ **设备管理改造** - 6.2.4阶段
- ⏳ **前端界面改造** - 6.3阶段

### 13.4 核心收益
1. **开发简化** - 租户处理对开发人员透明
2. **权限统一** - 基于用户类型的统一权限控制
3. **功能完整** - 企业级多租户管理功能
4. **扩展性强** - 支持未来功能扩展

## 14. 数据库迁移说明

### 14.1 数据库脚本文件
- **主要迁移脚本**: `main/manager-api/src/main/resources/db/changelog/************.sql`
- **初始数据脚本**: `main/manager-api/src/main/resources/db/changelog/************.sql`
- **回滚脚本**: `main/manager-api/src/main/resources/db/rollback/rollback-multi-tenant.sql`
- **性能优化脚本**: `main/manager-api/src/main/resources/db/optimization/multi-tenant-indexes.sql`

### 14.2 迁移方式
- **自动迁移**: 使用Liquibase，应用启动时自动执行
- **手动迁移**: 可手动执行SQL脚本
- **详细指南**: 参见 `docs/database-migration-guide.md`

### 14.3 前端开发注意事项
1. **本地开发环境**: 启动后端应用时会自动执行数据库迁移
2. **测试环境**: 确保数据库迁移成功后再进行前端测试
3. **API变更**: 所有API都已更新，请参考最新的API文档
4. **数据结构**: 注意新增的租户相关字段和表结构

**注意**: 本文档将随着后端改造进度持续更新，请前端开发人员密切关注变更内容。
