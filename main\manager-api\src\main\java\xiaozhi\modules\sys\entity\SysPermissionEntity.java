package xiaozhi.modules.sys.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import xiaozhi.common.entity.BaseEntity;

/**
 * 权限实体
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_permission")
public class SysPermissionEntity extends BaseEntity {
    
    /**
     * 权限字符串（domain:action:instance）
     */
    private String permission;
    
    /**
     * 权限描述
     */
    private String description;
    
    /**
     * 权限域（资源类型）
     */
    private String domain;
    
    /**
     * 操作类型
     */
    private String action;
    
    /**
     * 实例标识（可选，支持通配符*）
     */
    private String instance;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}
