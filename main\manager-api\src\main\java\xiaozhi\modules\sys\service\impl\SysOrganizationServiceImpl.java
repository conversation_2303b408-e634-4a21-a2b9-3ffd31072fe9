package xiaozhi.modules.sys.service.impl;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.PageData;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.sys.dao.SysOrganizationDao;
import xiaozhi.modules.sys.dto.OrganizationCreateRequestDTO;
import xiaozhi.modules.sys.dto.OrganizationDetailResponseDTO;
import xiaozhi.modules.sys.dto.OrganizationQueryRequestDTO;
import xiaozhi.modules.sys.dto.OrganizationUpdateRequestDTO;
import xiaozhi.modules.sys.entity.SysOrganizationEntity;
import xiaozhi.modules.sys.entity.SysTenantEntity;
import xiaozhi.modules.sys.service.SysOrganizationService;
import xiaozhi.modules.sys.service.SysTenantService;

import static com.baomidou.mybatisplus.extension.toolkit.Db.removeById;

/**
 * 企业组织Service实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class SysOrganizationServiceImpl extends BaseServiceImpl<SysOrganizationDao, SysOrganizationEntity> implements SysOrganizationService {

    private final SysOrganizationDao sysOrganizationDao;
    private final SysTenantService sysTenantService;
    
    @Override
    public SysOrganizationEntity getByOrgCode(String orgCode) {
        return sysOrganizationDao.getByOrgCode(orgCode);
    }
    
    @Override
    public List<SysOrganizationEntity> getOrganizationsByType(Integer orgType) {
        return sysOrganizationDao.getOrganizationsByType(orgType);
    }
    
    @Override
    public List<SysOrganizationEntity> getExpiringOrganizations(Integer days) {
        return sysOrganizationDao.getExpiringOrganizations(days);
    }

    @Override
    public PageData<OrganizationDetailResponseDTO> pageWithDetails(OrganizationQueryRequestDTO queryRequest) {
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.PAGE, queryRequest.getPage());
        params.put(Constant.LIMIT, queryRequest.getLimit());

        QueryWrapper<SysOrganizationEntity> wrapper = buildQueryWrapper(queryRequest);

        IPage<SysOrganizationEntity> page = baseDao.selectPage(
            getPage(params, "create_date", false),
            wrapper
        );

        List<OrganizationDetailResponseDTO> list = page.getRecords().stream()
            .map(this::convertToOrganizationDetail)
            .toList();

        return new PageData<>(list, page.getTotal());
    }

    @Override
    public OrganizationDetailResponseDTO getOrganizationDetail(Long orgId) {
        SysOrganizationEntity entity = baseDao.selectById(orgId);
        if (entity == null) {
            return null;
        }
        return convertToOrganizationDetail(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrganization(OrganizationCreateRequestDTO createRequest) {
        // 检查企业编码是否已存在
        if (existsOrgCode(createRequest.getOrgCode(), null)) {
            throw new RenException("企业编码已存在");
        }

        SysOrganizationEntity entity = new SysOrganizationEntity();
        entity.setOrgCode(createRequest.getOrgCode());
        entity.setOrgName(createRequest.getOrgName());
        entity.setOrgType(createRequest.getOrgType());
        entity.setContactPerson(createRequest.getContactPerson());
        entity.setContactPhone(createRequest.getContactPhone());
        entity.setContactEmail(createRequest.getContactEmail());
        entity.setAddress(createRequest.getAddress());
        entity.setStatus(createRequest.getStatus());
        entity.setExpireDate(createRequest.getExpireDate());
        entity.setRemark(createRequest.getRemark());

        insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrganization(OrganizationUpdateRequestDTO updateRequest) {
        SysOrganizationEntity entity = baseDao.selectById(updateRequest.getId());
        if (entity == null) {
            throw new RenException("企业不存在");
        }

        // 检查企业编码是否被其他企业使用
        if (existsOrgCode(updateRequest.getOrgCode(), updateRequest.getId())) {
            throw new RenException("企业编码已被其他企业使用");
        }

        entity.setOrgCode(updateRequest.getOrgCode());
        entity.setOrgName(updateRequest.getOrgName());
        entity.setOrgType(updateRequest.getOrgType());
        entity.setContactPerson(updateRequest.getContactPerson());
        entity.setContactPhone(updateRequest.getContactPhone());
        entity.setContactEmail(updateRequest.getContactEmail());
        entity.setAddress(updateRequest.getAddress());
        entity.setStatus(updateRequest.getStatus());
        entity.setExpireDate(updateRequest.getExpireDate());
        entity.setRemark(updateRequest.getRemark());

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrganization(Long orgId) {
        SysOrganizationEntity entity = baseDao.selectById(orgId);
        if (entity == null) {
            throw new RenException("企业不存在");
        }

        // 检查是否有关联的租户
        QueryWrapper<SysTenantEntity> tenantWrapper = new QueryWrapper<>();
        tenantWrapper.eq("org_id", orgId);
        tenantWrapper.eq("deleted", 0);
        Long tenantCount = sysTenantService.count(tenantWrapper);
        if (tenantCount > 0) {
            throw new RenException("企业下还有租户，无法删除");
        }

        // 软删除企业
        removeById(orgId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrganizationStatus(Long orgId, Integer status) {
        SysOrganizationEntity entity = new SysOrganizationEntity();
        entity.setId(orgId);
        entity.setStatus(status);
        updateById(entity);
    }

    @Override
    public boolean existsOrgCode(String orgCode, Long excludeId) {
        QueryWrapper<SysOrganizationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("org_code", orgCode);
        wrapper.eq("deleted", 0);
        if (excludeId != null) {
            wrapper.ne("id", excludeId);
        }
        return count(wrapper) > 0;
    }

    @Override
    public Map<String, Object> getOrganizationStatistics(Long orgId) {
        Map<String, Object> statistics = new HashMap<>();

        // 获取租户数量
        QueryWrapper<SysTenantEntity> tenantWrapper = new QueryWrapper<>();
        tenantWrapper.eq("org_id", orgId);
        tenantWrapper.eq("deleted", 0);
        Long tenantCount = sysTenantService.count(tenantWrapper);
        statistics.put("tenantCount", tenantCount);

        // TODO: 添加用户总数、设备总数、智能体总数统计
        // 这里需要根据实际的用户、设备、智能体服务来实现
        statistics.put("totalUsers", 0L);
        statistics.put("totalDevices", 0L);
        statistics.put("totalAgents", 0L);

        return statistics;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<SysOrganizationEntity> buildQueryWrapper(OrganizationQueryRequestDTO queryRequest) {
        QueryWrapper<SysOrganizationEntity> wrapper = new QueryWrapper<>();

        // 企业编码模糊查询
        if (StringUtils.isNotBlank(queryRequest.getOrgCode())) {
            wrapper.like("org_code", queryRequest.getOrgCode());
        }

        // 企业名称模糊查询
        if (StringUtils.isNotBlank(queryRequest.getOrgName())) {
            wrapper.like("org_name", queryRequest.getOrgName());
        }

        // 企业类型精确查询
        if (queryRequest.getOrgType() != null) {
            wrapper.eq("org_type", queryRequest.getOrgType());
        }

        // 联系人模糊查询
        if (StringUtils.isNotBlank(queryRequest.getContactPerson())) {
            wrapper.like("contact_person", queryRequest.getContactPerson());
        }

        // 联系电话模糊查询
        if (StringUtils.isNotBlank(queryRequest.getContactPhone())) {
            wrapper.like("contact_phone", queryRequest.getContactPhone());
        }

        // 状态精确查询
        if (queryRequest.getStatus() != null) {
            wrapper.eq("status", queryRequest.getStatus());
        }

        // 创建时间范围查询
        if (StringUtils.isNotBlank(queryRequest.getCreateDateStart())) {
            wrapper.ge("create_date", queryRequest.getCreateDateStart());
        }
        if (StringUtils.isNotBlank(queryRequest.getCreateDateEnd())) {
            wrapper.le("create_date", queryRequest.getCreateDateEnd());
        }

        // 到期时间范围查询
        if (StringUtils.isNotBlank(queryRequest.getExpireDateStart())) {
            wrapper.ge("expire_date", queryRequest.getExpireDateStart());
        }
        if (StringUtils.isNotBlank(queryRequest.getExpireDateEnd())) {
            wrapper.le("expire_date", queryRequest.getExpireDateEnd());
        }

        // 排除已删除的记录
        wrapper.eq("deleted", 0);

        // 默认按创建时间倒序排列
        wrapper.orderByDesc("create_date");

        return wrapper;
    }

    /**
     * 转换为企业详情DTO
     */
    private OrganizationDetailResponseDTO convertToOrganizationDetail(SysOrganizationEntity entity) {
        OrganizationDetailResponseDTO dto = ConvertUtils.sourceToTarget(entity, OrganizationDetailResponseDTO.class);

        // 设置企业类型名称
        dto.setOrgTypeName(entity.getOrgType() == 1 ? "普通企业" : "代理商");

        // 设置状态名称
        dto.setStatusName(entity.getStatus() == 1 ? "启用" : "禁用");

        // 计算剩余天数和是否即将到期
        if (entity.getExpireDate() != null) {
            LocalDate expireDate = entity.getExpireDate().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate now = LocalDate.now();
            long remainingDays = ChronoUnit.DAYS.between(now, expireDate);

            dto.setRemainingDays((int) remainingDays);
            dto.setIsExpiring(remainingDays <= 30 && remainingDays >= 0);
        }

        // 获取统计信息
        Map<String, Object> statistics = getOrganizationStatistics(entity.getId());
        dto.setTenantCount((Long) statistics.get("tenantCount"));
        dto.setTotalUsers((Long) statistics.get("totalUsers"));
        dto.setTotalDevices((Long) statistics.get("totalDevices"));
        dto.setTotalAgents((Long) statistics.get("totalAgents"));

        return dto;
    }
}
