package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 设备统计响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "设备统计响应")
public class DeviceStatisticsResponseDTO {
    
    @Schema(description = "设备ID", example = "device123")
    private String deviceId;
    
    @Schema(description = "设备MAC地址", example = "AA:BB:CC:DD:EE:FF")
    private String macAddress;
    
    @Schema(description = "设备别名", example = "客厅音箱")
    private String deviceAlias;
    
    @Schema(description = "总连接次数", example = "1000")
    private Integer totalConnectionCount;
    
    @Schema(description = "总在线时长（秒）", example = "36000")
    private Long totalOnlineDuration;
    
    @Schema(description = "平均在线时长（秒）", example = "36")
    private Long avgOnlineDuration;
    
    @Schema(description = "连接成功率", example = "0.95")
    private Double connectionSuccessRate;
    
    @Schema(description = "数据传输量（字节）", example = "1048576")
    private Long totalDataTransfer;
    
    @Schema(description = "最后连接时间")
    private Date lastConnectedAt;
    
    @Schema(description = "统计开始时间")
    private Date startDate;
    
    @Schema(description = "统计结束时间")
    private Date endDate;
    
    @Schema(description = "每日统计详情")
    private List<DailyStatistics> dailyStatistics;
    
    @Data
    @Schema(description = "每日统计")
    public static class DailyStatistics {
        
        @Schema(description = "统计日期")
        private Date date;
        
        @Schema(description = "连接次数")
        private Integer connectionCount;
        
        @Schema(description = "在线时长（秒）")
        private Long onlineDuration;
        
        @Schema(description = "成功连接次数")
        private Integer successConnectionCount;
        
        @Schema(description = "失败连接次数")
        private Integer failureConnectionCount;
        
        @Schema(description = "数据传输量（字节）")
        private Long dataTransferBytes;
    }
}
