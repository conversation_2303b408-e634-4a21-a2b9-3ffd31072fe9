/**
 * 权限配置文件
 * 定义系统中所有权限的详细信息
 */

/**
 * 权限定义
 * 格式：domain:action:instance
 * - domain: 业务域（如 user, device, agent 等）
 * - action: 操作类型（如 view, create, update, delete 等）
 * - instance: 实例范围（如 *, tenant, specific_id 等）
 */
export const PERMISSION_DEFINITIONS = {
  // 组织管理权限
  ORGANIZATION: {
    VIEW_ALL: {
      code: 'organization:view:*',
      name: '查看所有企业',
      description: '可以查看系统中所有企业信息'
    },
    CREATE: {
      code: 'organization:create:*',
      name: '创建企业',
      description: '可以创建新的企业'
    },
    UPDATE: {
      code: 'organization:update:*',
      name: '编辑企业',
      description: '可以编辑企业信息'
    },
    DELETE: {
      code: 'organization:delete:*',
      name: '删除企业',
      description: '可以删除企业'
    },
    MANAGE: {
      code: 'organization:manage:*',
      name: '管理企业',
      description: '拥有企业的完整管理权限'
    }
  },

  // 租户管理权限
  TENANT: {
    VIEW_ALL: {
      code: 'tenant:view:*',
      name: '查看所有租户',
      description: '可以查看系统中所有租户信息'
    },
    VIEW_TENANT: {
      code: 'tenant:view:tenant',
      name: '查看租户信息',
      description: '可以查看当前租户信息'
    },
    CREATE: {
      code: 'tenant:create:*',
      name: '创建租户',
      description: '可以创建新的租户'
    },
    UPDATE: {
      code: 'tenant:update:*',
      name: '编辑租户',
      description: '可以编辑租户信息'
    },
    DELETE: {
      code: 'tenant:delete:*',
      name: '删除租户',
      description: '可以删除租户'
    },
    MANAGE: {
      code: 'tenant:manage:*',
      name: '管理租户',
      description: '拥有租户的完整管理权限'
    }
  },

  // 用户管理权限
  USER: {
    VIEW_ALL: {
      code: 'user:view:*',
      name: '查看所有用户',
      description: '可以查看系统中所有用户信息'
    },
    VIEW_TENANT: {
      code: 'user:view:tenant',
      name: '查看租户用户',
      description: '可以查看当前租户的用户信息'
    },
    CREATE_ALL: {
      code: 'user:create:*',
      name: '创建任意用户',
      description: '可以创建任意类型的用户'
    },
    CREATE_TENANT: {
      code: 'user:create:tenant',
      name: '创建租户用户',
      description: '可以在当前租户下创建用户'
    },
    UPDATE_ALL: {
      code: 'user:update:*',
      name: '编辑任意用户',
      description: '可以编辑任意用户信息'
    },
    UPDATE_TENANT: {
      code: 'user:update:tenant',
      name: '编辑租户用户',
      description: '可以编辑当前租户的用户信息'
    },
    DELETE_ALL: {
      code: 'user:delete:*',
      name: '删除任意用户',
      description: '可以删除任意用户'
    },
    DELETE_TENANT: {
      code: 'user:delete:tenant',
      name: '删除租户用户',
      description: '可以删除当前租户的用户'
    },
    RESET_PASSWORD: {
      code: 'user:reset-password:*',
      name: '重置用户密码',
      description: '可以重置用户密码'
    },
    CHANGE_STATUS: {
      code: 'user:change-status:*',
      name: '修改用户状态',
      description: '可以启用或禁用用户'
    }
  },

  // 权限管理权限
  PERMISSION: {
    VIEW_ALL: {
      code: 'permission:view:*',
      name: '查看所有权限',
      description: '可以查看系统中所有权限信息'
    },
    VIEW_TENANT: {
      code: 'permission:view:tenant',
      name: '查看租户权限',
      description: '可以查看当前租户的权限信息'
    },
    ASSIGN_ALL: {
      code: 'permission:assign:*',
      name: '分配任意权限',
      description: '可以为任意用户分配权限'
    },
    ASSIGN_TENANT: {
      code: 'permission:assign:tenant',
      name: '分配租户权限',
      description: '可以为当前租户用户分配权限'
    },
    REVOKE: {
      code: 'permission:revoke:*',
      name: '撤销权限',
      description: '可以撤销用户权限'
    },
    MANAGE: {
      code: 'permission:manage:*',
      name: '管理权限',
      description: '拥有权限的完整管理权限'
    }
  },

  // 智能体管理权限
  AGENT: {
    VIEW_ALL: {
      code: 'agent:view:*',
      name: '查看所有智能体',
      description: '可以查看系统中所有智能体'
    },
    VIEW_TENANT: {
      code: 'agent:view:tenant',
      name: '查看租户智能体',
      description: '可以查看当前租户的智能体'
    },
    CREATE_ALL: {
      code: 'agent:create:*',
      name: '创建任意智能体',
      description: '可以创建任意智能体'
    },
    CREATE_TENANT: {
      code: 'agent:create:tenant',
      name: '创建租户智能体',
      description: '可以在当前租户下创建智能体'
    },
    UPDATE_ALL: {
      code: 'agent:update:*',
      name: '编辑任意智能体',
      description: '可以编辑任意智能体'
    },
    UPDATE_TENANT: {
      code: 'agent:update:tenant',
      name: '编辑租户智能体',
      description: '可以编辑当前租户的智能体'
    },
    DELETE_ALL: {
      code: 'agent:delete:*',
      name: '删除任意智能体',
      description: '可以删除任意智能体'
    },
    DELETE_TENANT: {
      code: 'agent:delete:tenant',
      name: '删除租户智能体',
      description: '可以删除当前租户的智能体'
    },
    ASSIGN: {
      code: 'agent:assign:*',
      name: '分配智能体',
      description: '可以将智能体分配给用户或设备'
    }
  },

  // 设备管理权限
  DEVICE: {
    VIEW_ALL: {
      code: 'device:view:*',
      name: '查看所有设备',
      description: '可以查看系统中所有设备'
    },
    VIEW_TENANT: {
      code: 'device:view:tenant',
      name: '查看租户设备',
      description: '可以查看当前租户的设备'
    },
    CREATE: {
      code: 'device:create:*',
      name: '创建设备',
      description: '可以创建新设备'
    },
    UPDATE: {
      code: 'device:update:*',
      name: '编辑设备',
      description: '可以编辑设备信息'
    },
    DELETE: {
      code: 'device:delete:*',
      name: '删除设备',
      description: '可以删除设备'
    },
    BIND_ALL: {
      code: 'device:bind:*',
      name: '绑定任意设备',
      description: '可以绑定任意设备'
    },
    BIND_TENANT: {
      code: 'device:bind:tenant',
      name: '绑定租户设备',
      description: '可以绑定当前租户的设备'
    },
    UNBIND_ALL: {
      code: 'device:unbind:*',
      name: '解绑任意设备',
      description: '可以解绑任意设备'
    },
    UNBIND_TENANT: {
      code: 'device:unbind:tenant',
      name: '解绑租户设备',
      description: '可以解绑当前租户的设备'
    },
    ACTIVATE: {
      code: 'device:activate:*',
      name: '激活设备',
      description: '可以激活设备'
    }
  },

  // 系统管理权限
  SYSTEM: {
    CONFIG: {
      code: 'system:config:*',
      name: '系统配置',
      description: '可以修改系统配置'
    },
    MONITOR: {
      code: 'system:monitor:*',
      name: '系统监控',
      description: '可以查看系统监控信息'
    },
    LOG: {
      code: 'system:log:*',
      name: '系统日志',
      description: '可以查看系统日志'
    }
  },

  // 超级管理员权限
  SUPER_ADMIN: {
    ALL: {
      code: '*:*:*',
      name: '超级管理员',
      description: '拥有系统的所有权限'
    }
  }
}

/**
 * 角色权限映射
 */
export const ROLE_PERMISSIONS = {
  // 平台管理员 - 拥有所有权限
  PLATFORM_ADMIN: [
    PERMISSION_DEFINITIONS.SUPER_ADMIN.ALL.code
  ],

  // 租户管理员 - 拥有租户范围内的管理权限
  TENANT_ADMIN: [
    PERMISSION_DEFINITIONS.TENANT.VIEW_TENANT.code,
    PERMISSION_DEFINITIONS.USER.VIEW_TENANT.code,
    PERMISSION_DEFINITIONS.USER.CREATE_TENANT.code,
    PERMISSION_DEFINITIONS.USER.UPDATE_TENANT.code,
    PERMISSION_DEFINITIONS.USER.DELETE_TENANT.code,
    PERMISSION_DEFINITIONS.USER.RESET_PASSWORD.code,
    PERMISSION_DEFINITIONS.USER.CHANGE_STATUS.code,
    PERMISSION_DEFINITIONS.PERMISSION.VIEW_TENANT.code,
    PERMISSION_DEFINITIONS.PERMISSION.ASSIGN_TENANT.code,
    PERMISSION_DEFINITIONS.AGENT.VIEW_TENANT.code,
    PERMISSION_DEFINITIONS.AGENT.CREATE_TENANT.code,
    PERMISSION_DEFINITIONS.AGENT.UPDATE_TENANT.code,
    PERMISSION_DEFINITIONS.AGENT.DELETE_TENANT.code,
    PERMISSION_DEFINITIONS.DEVICE.VIEW_TENANT.code,
    PERMISSION_DEFINITIONS.DEVICE.BIND_TENANT.code,
    PERMISSION_DEFINITIONS.DEVICE.UNBIND_TENANT.code
  ],

  // 普通用户 - 只有查看权限
  NORMAL_USER: [
    PERMISSION_DEFINITIONS.AGENT.VIEW_TENANT.code,
    PERMISSION_DEFINITIONS.DEVICE.VIEW_TENANT.code
  ]
}

/**
 * 获取所有权限列表
 */
export function getAllPermissions() {
  const permissions = []
  
  Object.values(PERMISSION_DEFINITIONS).forEach(domain => {
    Object.values(domain).forEach(permission => {
      permissions.push({
        code: permission.code,
        name: permission.name,
        description: permission.description
      })
    })
  })
  
  return permissions
}

/**
 * 根据角色获取权限
 */
export function getPermissionsByRole(role) {
  return ROLE_PERMISSIONS[role] || []
}
