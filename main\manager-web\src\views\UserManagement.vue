<template>
  <div class="user-management">
    <HeaderBar />

    <div class="main-content">
      <div class="content-header">
        <h2>用户管理</h2>
        <div class="header-actions">
          <el-button
            type="primary"
            @click="showCreateDialog = true"
            v-permission="[PERMISSIONS.USER.CREATE_ALL, PERMISSIONS.USER.CREATE_TENANT]">
            <i class="el-icon-plus"></i> 新增用户
          </el-button>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="用户名">
            <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
          </el-form-item>
          <el-form-item label="真实姓名">
            <el-input v-model="searchForm.realName" placeholder="请输入真实姓名" clearable />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="searchForm.mobile" placeholder="请输入手机号" clearable />
          </el-form-item>
          <el-form-item label="用户类型">
            <el-select v-model="searchForm.userType" placeholder="请选择用户类型" clearable>
              <el-option label="平台管理员" :value="1" />
              <el-option label="租户管理员" :value="2" />
              <el-option label="普通用户" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-area">
        <el-table
          :data="userList"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="realName" label="真实姓名" width="120" />
          <el-table-column prop="userTypeName" label="用户类型" width="120" />
          <el-table-column prop="mobile" label="手机号" width="130" />
          <el-table-column prop="email" label="邮箱" min-width="180" />
          <el-table-column
            prop="tenantName"
            label="所属租户"
            width="150"
            v-permission="PERMISSIONS.ORGANIZATION.VIEW_ALL" />
          <el-table-column prop="statusName" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.statusName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createDate" label="创建时间" width="120" />
          <el-table-column label="操作" width="250" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleView(scope.row)">查看</el-button>
              <el-button
                size="mini"
                type="primary"
                @click="handleEdit(scope.row)"
                v-permission="[PERMISSIONS.USER.UPDATE_ALL, PERMISSIONS.USER.UPDATE_TENANT]">
                编辑
              </el-button>
              <el-button
                size="mini"
                type="warning"
                @click="handleResetPassword(scope.row)"
                v-permission="PERMISSIONS.USER.RESET_PASSWORD">
                重置密码
              </el-button>
              <el-dropdown
                @command="handleCommand"
                v-permission="[PERMISSIONS.USER.DELETE_ALL, PERMISSIONS.USER.DELETE_TENANT, PERMISSIONS.USER.CHANGE_STATUS]">
                <el-button size="mini">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :command="{action: 'changeStatus', row: scope.row}"
                    v-permission="PERMISSIONS.USER.CHANGE_STATUS">
                    {{ scope.row.status === 1 ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="{action: 'delete', row: scope.row}"
                    v-permission="[PERMISSIONS.USER.DELETE_ALL, PERMISSIONS.USER.DELETE_TENANT]">
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 批量操作 -->
        <div class="batch-operations" v-if="selectedUsers.length > 0">
          <span>已选择 {{ selectedUsers.length }} 项</span>
          <el-button
            size="mini"
            type="success"
            @click="handleBatchEnable"
            v-permission="PERMISSIONS.USER.CHANGE_STATUS">
            批量启用
          </el-button>
          <el-button
            size="mini"
            type="warning"
            @click="handleBatchDisable"
            v-permission="PERMISSIONS.USER.CHANGE_STATUS">
            批量禁用
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleBatchDelete"
            v-permission="[PERMISSIONS.USER.DELETE_ALL, PERMISSIONS.USER.DELETE_TENANT]">
            批量删除
          </el-button>
        </div>

        <!-- 分页 -->
        <div class="pagination-area">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="showCreateDialog"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form :model="userForm" :rules="formRules" ref="userForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="userForm.username" placeholder="请输入用户名" :disabled="isEdit" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="!isEdit">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input v-model="userForm.password" type="password" placeholder="请输入密码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input v-model="userForm.confirmPassword" type="password" placeholder="请确认密码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="userForm.mobile" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userForm.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户类型" prop="userType">
              <el-select v-model="userForm.userType" placeholder="请选择用户类型" style="width: 100%">
                <el-option
                  label="平台管理员"
                  :value="1"
                  v-permission="PERMISSIONS.ORGANIZATION.VIEW_ALL" />
                <el-option label="租户管理员" :value="2" />
                <el-option label="普通用户" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别">
              <el-radio-group v-model="userForm.gender">
                <el-radio :label="0">保密</el-radio>
                <el-radio :label="1">男</el-radio>
                <el-radio :label="2">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="状态">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </el-dialog>

    <!-- 查看用户详情对话框 -->
    <el-dialog title="用户详情" :visible.sync="showDetailDialog" width="600px">
      <div class="detail-content" v-if="currentUser">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>用户名：</label>
              <span>{{ currentUser.username }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>真实姓名：</label>
              <span>{{ currentUser.realName }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>手机号：</label>
              <span>{{ currentUser.mobile }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>邮箱：</label>
              <span>{{ currentUser.email }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>用户类型：</label>
              <span>{{ currentUser.userTypeName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>性别：</label>
              <span>{{ getGenderText(currentUser.gender) }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12" v-permission="PERMISSIONS.ORGANIZATION.VIEW_ALL">
            <div class="detail-item">
              <label>所属租户：</label>
              <span>{{ currentUser.tenantName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="currentUser.status === 1 ? 'success' : 'danger'">
                {{ currentUser.statusName }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <div class="detail-item">
          <label>创建时间：</label>
          <span>{{ currentUser.createDate }}</span>
        </div>
      </div>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog title="重置密码" :visible.sync="showPasswordDialog" width="400px">
      <div v-if="newPassword">
        <p>用户密码已重置，新密码为：</p>
        <div class="password-display">
          <el-input v-model="newPassword" readonly>
            <el-button slot="append" @click="copyPassword">复制</el-button>
          </el-input>
        </div>
        <p class="password-tip">请将新密码告知用户，并提醒用户及时修改密码。</p>
      </div>
    </el-dialog>

    <VersionFooter />
  </div>
</template>

<script>
import HeaderBar from "@/components/HeaderBar.vue";
import VersionFooter from "@/components/VersionFooter.vue";
import userApi from "@/apis/module/user";

export default {
  name: "UserManagement",
  components: {
    HeaderBar,
    VersionFooter
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        username: '',
        realName: '',
        mobile: '',
        userType: null,
        status: null
      },
      // 数据列表
      userList: [],
      selectedUsers: [],
      loading: false,
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 对话框
      showCreateDialog: false,
      showDetailDialog: false,
      showPasswordDialog: false,
      dialogTitle: '新增用户',
      isEdit: false,
      submitting: false,
      // 表单数据
      userForm: {
        id: null,
        username: '',
        realName: '',
        password: '',
        confirmPassword: '',
        mobile: '',
        email: '',
        userType: 3,
        gender: 0,
        status: 1
      },
      // 当前查看的用户
      currentUser: null,
      newPassword: '',
      // 表单验证规则
      formRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        userType: [
          { required: true, message: '请选择用户类型', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    // 权限控制方法已通过混入提供，无需重复定义
  },
  created() {
    this.fetchUsers();
  },
  methods: {
    // 验证确认密码
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.userForm.password) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    },

    // 获取用户列表
    fetchUsers() {
      this.loading = true;
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        ...this.searchForm
      };

      userApi.getUserPage(params, (res) => {
        this.loading = false;
        if (res.code === 0) {
          this.userList = res.data.list || [];
          this.total = res.data.total || 0;
        } else {
          this.$message.error(res.msg || '获取用户列表失败哈哈');
        }
      });
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.fetchUsers();
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        username: '',
        realName: '',
        mobile: '',
        userType: null,
        status: null
      };
      this.currentPage = 1;
      this.fetchUsers();
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.fetchUsers();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchUsers();
    },

    // 选择改变
    handleSelectionChange(selection) {
      this.selectedUsers = selection;
    },
    // 查看详情
    handleView(row) {
      this.currentUser = row;
      this.showDetailDialog = true;
    },

    // 编辑
    handleEdit(row) {
      this.isEdit = true;
      this.dialogTitle = '编辑用户';
      this.userForm = {
        ...row,
        password: '',
        confirmPassword: ''
      };
      this.showCreateDialog = true;
    },

    // 下拉菜单命令处理
    handleCommand(command) {
      const { action, row } = command;
      if (action === 'changeStatus') {
        this.handleChangeStatus(row);
      } else if (action === 'delete') {
        this.handleDelete(row);
      }
    },

    // 重置密码
    handleResetPassword(row) {
      this.$confirm('确定要重置该用户的密码吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userApi.resetUserPassword(row.id, (res) => {
          if (res.code === 0) {
            this.newPassword = res.data || '123456';
            this.showPasswordDialog = true;
            this.$message.success('密码重置成功');
          } else {
            this.$message.error(res.msg || '密码重置失败');
          }
        });
      });
    },

    // 修改状态
    handleChangeStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? '启用' : '禁用';

      this.$confirm(`确定要${statusText}该用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userApi.changeUserStatus(row.id, newStatus, (res) => {
          if (res.code === 0) {
            this.$message.success(`${statusText}成功`);
            this.fetchUsers();
          } else {
            this.$message.error(res.msg || `${statusText}失败`);
          }
        });
      });
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确定要删除该用户吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userApi.deleteUser(row.id, (res) => {
          if (res.code === 0) {
            this.$message.success('删除成功');
            this.fetchUsers();
          } else {
            this.$message.error(res.msg || '删除失败');
          }
        });
      });
    },

    // 批量启用
    handleBatchEnable() {
      this.handleBatchChangeStatus(1);
    },

    // 批量禁用
    handleBatchDisable() {
      this.handleBatchChangeStatus(0);
    },

    // 批量修改状态
    handleBatchChangeStatus(status) {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请先选择用户');
        return;
      }

      const statusText = status === 1 ? '启用' : '禁用';
      this.$confirm(`确定要${statusText}选中的${this.selectedUsers.length}个用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const promises = this.selectedUsers.map(user => {
          return new Promise((resolve) => {
            userApi.changeUserStatus(user.id, status, (res) => {
              resolve(res.code === 0);
            });
          });
        });

        Promise.all(promises).then(results => {
          const successCount = results.filter(r => r).length;
          if (successCount === results.length) {
            this.$message.success(`批量${statusText}成功`);
          } else {
            this.$message.warning(`${successCount}个用户${statusText}成功，${results.length - successCount}个失败`);
          }
          this.fetchUsers();
        });
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请先选择用户');
        return;
      }

      this.$confirm(`确定要删除选中的${this.selectedUsers.length}个用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const promises = this.selectedUsers.map(user => {
          return new Promise((resolve) => {
            userApi.deleteUser(user.id, (res) => {
              resolve(res.code === 0);
            });
          });
        });

        Promise.all(promises).then(results => {
          const successCount = results.filter(r => r).length;
          if (successCount === results.length) {
            this.$message.success('批量删除成功');
          } else {
            this.$message.warning(`${successCount}个用户删除成功，${results.length - successCount}个失败`);
          }
          this.fetchUsers();
        });
      });
    },
    // 提交表单
    handleSubmit() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          this.submitting = true;
          const apiMethod = this.isEdit ? 'updateUser' : 'createUser';

          userApi[apiMethod](this.userForm, (res) => {
            this.submitting = false;
            if (res.code === 0) {
              this.$message.success(this.isEdit ? '更新成功' : '创建成功');
              this.showCreateDialog = false;
              this.fetchUsers();
            } else {
              this.$message.error(res.msg || (this.isEdit ? '更新失败' : '创建失败'));
            }
          });
        }
      });
    },

    // 对话框关闭
    handleDialogClose() {
      this.isEdit = false;
      this.dialogTitle = '新增用户';
      this.userForm = {
        id: null,
        username: '',
        realName: '',
        password: '',
        confirmPassword: '',
        mobile: '',
        email: '',
        userType: 3,
        gender: 0,
        status: 1
      };
      this.$refs.userForm && this.$refs.userForm.resetFields();
    },

    // 复制密码
    copyPassword() {
      const input = document.createElement('input');
      input.value = this.newPassword;
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$message.success('密码已复制到剪贴板');
    },

    // 获取性别文本
    getGenderText(gender) {
      const genderMap = {
        0: '保密',
        1: '男',
        2: '女'
      };
      return genderMap[gender] || '保密';
    }
  }
};
</script>

<style scoped>
.user-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.content-header h2 {
  margin: 0;
  color: #333;
}

.search-area {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-area {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.batch-operations {
  margin-top: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.batch-operations span {
  color: #666;
  font-size: 14px;
}

.pagination-area {
  margin-top: 20px;
  text-align: right;
}

.detail-content {
  padding: 10px 0;
}

.detail-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
}

.detail-item label {
  width: 100px;
  font-weight: bold;
  color: #666;
  flex-shrink: 0;
}

.detail-item span {
  flex: 1;
  word-break: break-all;
}

.password-display {
  margin: 15px 0;
}

.password-tip {
  color: #666;
  font-size: 12px;
  margin-top: 10px;
}
</style>
