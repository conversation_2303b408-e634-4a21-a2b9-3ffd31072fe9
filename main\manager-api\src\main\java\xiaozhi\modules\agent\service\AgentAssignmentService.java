package xiaozhi.modules.agent.service;

import java.util.List;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.agent.dto.AgentAssignmentQueryDTO;
import xiaozhi.modules.agent.dto.AgentAssignmentRequestDTO;
import xiaozhi.modules.agent.entity.AgentAssignmentEntity;

/**
 * 智能体分配服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface AgentAssignmentService extends BaseService<AgentAssignmentEntity> {
    
    /**
     * 分配智能体给用户
     *
     * @param assignmentRequest 分配请求
     */
    void assignAgentsToUsers(AgentAssignmentRequestDTO assignmentRequest);
    
    /**
     * 取消智能体分配
     *
     * @param assignmentRequest 取消分配请求
     */
    void unassignAgentsFromUsers(AgentAssignmentRequestDTO assignmentRequest);
    
    /**
     * 分页查询智能体分配记录
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    PageData<AgentAssignmentEntity> pageAssignments(AgentAssignmentQueryDTO queryDTO);
    
    /**
     * 检查智能体是否已分配给用户
     *
     * @param agentId 智能体ID
     * @param userId 用户ID
     * @return 是否已分配
     */
    boolean isAgentAssignedToUser(String agentId, Long userId);
    
    /**
     * 获取用户已分配的智能体ID列表
     *
     * @param userId 用户ID
     * @return 智能体ID列表
     */
    List<String> getAssignedAgentIdsByUserId(Long userId);
    
    /**
     * 获取智能体已分配的用户ID列表
     *
     * @param agentId 智能体ID
     * @return 用户ID列表
     */
    List<Long> getAssignedUserIdsByAgentId(String agentId);
    
    /**
     * 根据智能体ID获取分配记录
     *
     * @param agentId 智能体ID
     * @return 分配记录列表
     */
    List<AgentAssignmentEntity> getAssignmentsByAgentId(String agentId);
    
    /**
     * 根据用户ID获取分配记录
     *
     * @param userId 用户ID
     * @return 分配记录列表
     */
    List<AgentAssignmentEntity> getAssignmentsByUserId(Long userId);
}
