package xiaozhi.modules.sys.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import xiaozhi.common.entity.BaseEntity;

/**
 * 租户实体
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_tenant")
public class SysTenantEntity extends BaseEntity {
    
    /**
     * 租户编码
     */
    private String tenantCode;
    
    /**
     * 租户名称
     */
    private String tenantName;
    
    /**
     * 关联企业ID
     */
    private Long orgId;
    
    /**
     * 租户管理员用户ID
     */
    private Long adminUserId;
    
    /**
     * 最大用户数
     */
    private Integer maxUsers;
    
    /**
     * 最大设备数
     */
    private Integer maxDevices;
    
    /**
     * 最大智能体数
     */
    private Integer maxAgents;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 到期时间
     */
    private Date expireDate;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}
