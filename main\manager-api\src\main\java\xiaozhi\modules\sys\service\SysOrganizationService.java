package xiaozhi.modules.sys.service;

import java.util.List;
import java.util.Map;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.sys.dto.OrganizationCreateRequestDTO;
import xiaozhi.modules.sys.dto.OrganizationDetailResponseDTO;
import xiaozhi.modules.sys.dto.OrganizationQueryRequestDTO;
import xiaozhi.modules.sys.dto.OrganizationUpdateRequestDTO;
import xiaozhi.modules.sys.entity.SysOrganizationEntity;

/**
 * 企业组织Service
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface SysOrganizationService extends BaseService<SysOrganizationEntity> {
    
    /**
     * 根据企业编码获取企业信息
     * 
     * @param orgCode 企业编码
     * @return 企业实体
     */
    SysOrganizationEntity getByOrgCode(String orgCode);
    
    /**
     * 根据企业类型获取企业列表
     * 
     * @param orgType 企业类型
     * @return 企业实体列表
     */
    List<SysOrganizationEntity> getOrganizationsByType(Integer orgType);
    
    /**
     * 获取即将到期的企业列表
     *
     * @param days 天数
     * @return 企业实体列表
     */
    List<SysOrganizationEntity> getExpiringOrganizations(Integer days);

    /**
     * 分页查询企业详情
     *
     * @param queryRequest 查询请求
     * @return 分页数据
     */
    PageData<OrganizationDetailResponseDTO> pageWithDetails(OrganizationQueryRequestDTO queryRequest);

    /**
     * 根据ID获取企业详情
     *
     * @param orgId 企业ID
     * @return 企业详情
     */
    OrganizationDetailResponseDTO getOrganizationDetail(Long orgId);

    /**
     * 创建企业
     *
     * @param createRequest 创建请求
     */
    void createOrganization(OrganizationCreateRequestDTO createRequest);

    /**
     * 更新企业
     *
     * @param updateRequest 更新请求
     */
    void updateOrganization(OrganizationUpdateRequestDTO updateRequest);

    /**
     * 删除企业
     *
     * @param orgId 企业ID
     */
    void deleteOrganization(Long orgId);

    /**
     * 更新企业状态
     *
     * @param orgId 企业ID
     * @param status 状态
     */
    void updateOrganizationStatus(Long orgId, Integer status);

    /**
     * 检查企业编码是否存在
     *
     * @param orgCode 企业编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsOrgCode(String orgCode, Long excludeId);

    /**
     * 获取企业统计信息
     *
     * @param orgId 企业ID
     * @return 统计信息
     */
    Map<String, Object> getOrganizationStatistics(Long orgId);
}
