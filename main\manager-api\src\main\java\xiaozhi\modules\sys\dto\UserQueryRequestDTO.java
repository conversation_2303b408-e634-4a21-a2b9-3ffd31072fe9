package xiaozhi.modules.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 用户查询请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "用户查询请求")
public class UserQueryRequestDTO {
    
    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private String page = "1";
    
    @Schema(description = "每页数量", example = "10")
    @Min(value = 1, message = "每页数量必须大于0")
    private String limit = "10";
    
    @Schema(description = "用户名", example = "admin")
    private String username;
    
//    @Schema(description = "真实姓名", example = "张三")
//    private String realName;
//
//    @Schema(description = "手机号", example = "13800138000")
//    private String mobile;
//
//    @Schema(description = "邮箱", example = "<EMAIL>")
//    private String email;
    
    @Schema(description = "用户类型", example = "1")
    private Integer userType;
    
    @Schema(description = "状态", example = "1")
    private Integer status;
    
//    @Schema(description = "部门ID", example = "1")
//    private Long deptId;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;
    
    @Schema(description = "创建开始时间", example = "2025-01-01")
    private String createStartDate;
    
    @Schema(description = "创建结束时间", example = "2025-01-31")
    private String createEndDate;
    
    @Schema(description = "关键词搜索", example = "张三")
    private String keywords;
}
