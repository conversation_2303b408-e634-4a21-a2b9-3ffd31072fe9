# Shiro多租户权限配置指南

## 1. Shiro配置概述

基于Shiro最佳实践，采用`domain:action:instance`格式的权限字符串，实现细粒度的权限控制和多租户数据隔离。

## 2. 权限字符串设计

### 2.1 格式说明
```
domain:action:instance
```

- **domain**: 权限域（资源类型）
- **action**: 操作类型
- **instance**: 实例标识（可选，支持通配符*）

### 2.2 权限示例
```java
// 查看所有企业
"organization:view:*"

// 查看租户内用户
"user:view:tenant"

// 绑定租户设备
"device:bind:tenant"

// 查看特定设备
"device:view:12345"
```

## 3. Shiro配置类

### 3.1 主配置类
```java
@Configuration
public class ShiroConfig {
    
    @Bean
    public ShiroFilterFactoryBean shiroFilterFactoryBean(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);
        
        // 设置登录页面
        shiroFilterFactoryBean.setLoginUrl("/login");
        // 设置成功页面
        shiroFilterFactoryBean.setSuccessUrl("/index");
        // 设置未授权页面
        shiroFilterFactoryBean.setUnauthorizedUrl("/unauthorized");
        
        // 设置过滤器链
        Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>();
        filterChainDefinitionMap.put("/login", "anon");
        filterChainDefinitionMap.put("/logout", "logout");
        filterChainDefinitionMap.put("/static/**", "anon");
        filterChainDefinitionMap.put("/**", "authc");
        
        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        return shiroFilterFactoryBean;
    }
    
    @Bean
    public SecurityManager securityManager() {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        securityManager.setRealm(customRealm());
        return securityManager;
    }
    
    @Bean
    public CustomRealm customRealm() {
        CustomRealm customRealm = new CustomRealm();
        customRealm.setPermissionResolver(new WildcardPermissionResolver());
        return customRealm;
    }
}
```

### 3.2 自定义Realm
```java
@Component
public class CustomRealm extends AuthorizingRealm {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private PermissionService permissionService;
    
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        String username = (String) principals.getPrimaryPrincipal();
        UserEntity user = userService.findByUsername(username);
        
        SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
        
        // 获取用户角色
        Set<String> roles = userService.getUserRoles(user.getId());
        authorizationInfo.setRoles(roles);
        
        // 获取用户权限
        Set<String> permissions = permissionService.getUserPermissions(user.getId());
        authorizationInfo.setStringPermissions(permissions);
        
        return authorizationInfo;
    }
    
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) 
            throws AuthenticationException {
        String username = (String) token.getPrincipal();
        UserEntity user = userService.findByUsername(username);
        
        if (user == null) {
            throw new UnknownAccountException("用户不存在");
        }
        
        if (user.getStatus() == 0) {
            throw new LockedAccountException("用户已被锁定");
        }
        
        return new SimpleAuthenticationInfo(username, user.getPassword(), getName());
    }
}
```

## 4. 权限校验实现

### 4.1 注解式权限校验
```java
@RestController
@RequestMapping("/api/device")
public class DeviceController {
    
    @GetMapping("/list")
    @RequiresPermissions("device:view:tenant")
    public Result<List<Device>> getDeviceList() {
        // 业务逻辑
        return Result.ok(deviceService.getDeviceList());
    }
    
    @PostMapping("/bind")
    @RequiresPermissions("device:bind:tenant")
    public Result<Void> bindDevice(@RequestBody DeviceBindDTO dto) {
        deviceService.bindDevice(dto);
        return Result.ok();
    }
    
    @DeleteMapping("/{id}")
    @RequiresPermissions("device:delete:tenant")
    public Result<Void> deleteDevice(@PathVariable String id) {
        deviceService.deleteDevice(id);
        return Result.ok();
    }
}
```

### 4.2 编程式权限校验
```java
@Service
public class DeviceService {
    
    public List<Device> getDeviceList() {
        Subject subject = SecurityUtils.getSubject();
        
        if (subject.isPermitted("device:view:*")) {
            // 平台管理员，查看所有设备
            return deviceMapper.selectAll();
        } else if (subject.isPermitted("device:view:tenant")) {
            // 租户用户，只查看租户设备
            Long tenantId = getCurrentTenantId();
            return deviceMapper.selectByTenantId(tenantId);
        } else {
            throw new UnauthorizedException("无权限访问设备信息");
        }
    }
    
    public void deleteDevice(String deviceId) {
        Subject subject = SecurityUtils.getSubject();
        
        // 动态权限校验
        String permission = "device:delete:" + deviceId;
        if (!subject.isPermitted(permission) && !subject.isPermitted("device:delete:tenant")) {
            throw new UnauthorizedException("无权限删除该设备");
        }
        
        deviceMapper.deleteById(deviceId);
    }
}
```

## 5. 租户权限解析器

### 5.1 租户权限解析器
```java
@Component
public class TenantPermissionResolver {
    
    public boolean isPermitted(String permission, Long tenantId) {
        Subject subject = SecurityUtils.getSubject();
        
        // 检查通配符权限
        if (subject.isPermitted(permission.replace("tenant", "*"))) {
            return true;
        }
        
        // 检查租户权限
        if (tenantId != null) {
            String tenantPermission = permission.replace("tenant", String.valueOf(tenantId));
            if (subject.isPermitted(tenantPermission)) {
                return true;
            }
        }
        
        // 检查基础租户权限
        return subject.isPermitted(permission);
    }
}
```

### 5.2 权限工具类
```java
@Component
public class PermissionUtils {
    
    @Autowired
    private TenantPermissionResolver tenantPermissionResolver;
    
    /**
     * 检查用户是否有指定权限
     */
    public boolean hasPermission(String permission) {
        Subject subject = SecurityUtils.getSubject();
        return subject.isPermitted(permission);
    }
    
    /**
     * 检查用户是否有租户权限
     */
    public boolean hasTenantPermission(String permission, Long tenantId) {
        return tenantPermissionResolver.isPermitted(permission, tenantId);
    }
    
    /**
     * 检查用户是否有任意一个权限
     */
    public boolean hasAnyPermission(String... permissions) {
        Subject subject = SecurityUtils.getSubject();
        for (String permission : permissions) {
            if (subject.isPermitted(permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查用户是否有所有权限
     */
    public boolean hasAllPermissions(String... permissions) {
        Subject subject = SecurityUtils.getSubject();
        for (String permission : permissions) {
            if (!subject.isPermitted(permission)) {
                return false;
            }
        }
        return true;
    }
}
```

## 6. 前端权限控制

### 6.1 菜单权限控制
```javascript
// 根据权限显示菜单
export function hasPermission(permission) {
  const permissions = store.getters.permissions;
  return permissions.includes(permission) || permissions.includes('*:*:*');
}

// 菜单配置
const menuConfig = [
  {
    path: '/organization',
    component: 'organization/index',
    meta: {
      title: '企业管理',
      permission: 'organization:view:*'
    }
  },
  {
    path: '/device',
    component: 'device/index',
    meta: {
      title: '设备管理',
      permission: 'device:view:tenant'
    }
  }
];

// 过滤菜单
const filteredMenu = menuConfig.filter(menu => {
  return hasPermission(menu.meta.permission);
});
```

### 6.2 按钮权限控制
```vue
<template>
  <div>
    <el-button 
      v-if="hasPermission('device:create:tenant')"
      @click="createDevice">
      新增设备
    </el-button>
    
    <el-button 
      v-if="hasPermission('device:delete:tenant')"
      @click="deleteDevice">
      删除设备
    </el-button>
  </div>
</template>

<script>
export default {
  methods: {
    hasPermission(permission) {
      return this.$store.getters.permissions.includes(permission);
    }
  }
}
</script>
```

## 7. 最佳实践

### 7.1 权限设计原则
1. **最小权限原则**：用户只拥有完成工作所需的最小权限
2. **权限分离**：不同类型的操作使用不同的权限
3. **层次化设计**：权限具有明确的层次结构
4. **易于扩展**：新增功能时容易添加相应权限

### 7.2 性能优化
1. **权限缓存**：将用户权限信息缓存到Redis
2. **批量查询**：一次性查询用户的所有权限
3. **懒加载**：按需加载权限信息
4. **权限预计算**：提前计算复杂的权限逻辑

### 7.3 安全建议
1. **权限校验**：在每个接口都进行权限校验
2. **数据过滤**：根据权限过滤返回的数据
3. **日志记录**：记录权限相关的操作日志
4. **定期审计**：定期审计用户权限分配情况

这个配置指南提供了完整的Shiro多租户权限实现方案，确保系统的安全性和可扩展性。
