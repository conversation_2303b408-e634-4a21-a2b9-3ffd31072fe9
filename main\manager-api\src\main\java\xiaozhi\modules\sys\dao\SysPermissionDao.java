package xiaozhi.modules.sys.dao;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import xiaozhi.common.dao.BaseDao;
import xiaozhi.modules.sys.entity.SysPermissionEntity;

/**
 * 权限DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Mapper
public interface SysPermissionDao extends BaseDao<SysPermissionEntity> {
    
    /**
     * 根据用户ID获取权限列表
     * 
     * @param userId 用户ID
     * @return 权限字符串集合
     */
    Set<String> getUserPermissions(@Param("userId") Long userId);
    
    /**
     * 根据角色ID获取权限列表
     * 
     * @param roleId 角色ID
     * @return 权限实体列表
     */
    List<SysPermissionEntity> getPermissionsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据权限域和操作获取权限列表
     * 
     * @param domain 权限域
     * @param action 操作类型
     * @return 权限实体列表
     */
    List<SysPermissionEntity> getPermissionsByDomainAndAction(@Param("domain") String domain, @Param("action") String action);
}
