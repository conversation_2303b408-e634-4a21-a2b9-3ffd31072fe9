package xiaozhi.modules.device.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.device.dao.DeviceUsageStatisticsDao;
import xiaozhi.modules.device.dto.DeviceStatisticsQueryDTO;
import xiaozhi.modules.device.dto.DeviceStatisticsResponseDTO;
import xiaozhi.modules.device.entity.DeviceUsageStatisticsEntity;
import xiaozhi.modules.device.service.DeviceStatisticsService;
import xiaozhi.modules.security.tenant.TenantContext;

/**
 * 设备统计服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Slf4j
@AllArgsConstructor
@Service
public class DeviceStatisticsServiceImpl extends BaseServiceImpl<DeviceUsageStatisticsDao, DeviceUsageStatisticsEntity> implements DeviceStatisticsService {
    
    private final DeviceUsageStatisticsDao deviceUsageStatisticsDao;
    
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    
    @Override
    public DeviceStatisticsResponseDTO getDeviceStatistics(DeviceStatisticsQueryDTO queryDTO) {
        try {
            Date startDate = dateFormat.parse(queryDTO.getStartDate());
            Date endDate = dateFormat.parse(queryDTO.getEndDate());
            
            // 获取总体统计信息
            Map<String, Object> overallStats = deviceUsageStatisticsDao.getDeviceOverallStatistics(
                queryDTO.getDeviceId(), startDate, endDate);
            
            // 获取每日统计详情
            List<DeviceUsageStatisticsEntity> dailyStats = deviceUsageStatisticsDao.getStatisticsByDeviceAndDateRange(
                queryDTO.getDeviceId(), startDate, endDate);
            
            // 构建响应DTO
            DeviceStatisticsResponseDTO response = new DeviceStatisticsResponseDTO();
            response.setDeviceId(queryDTO.getDeviceId());
            response.setMacAddress(queryDTO.getMacAddress());
            response.setStartDate(startDate);
            response.setEndDate(endDate);
            
            if (overallStats != null) {
                response.setTotalConnectionCount((Integer) overallStats.get("totalConnectionCount"));
                response.setTotalOnlineDuration((Long) overallStats.get("totalOnlineDuration"));
                response.setAvgOnlineDuration((Long) overallStats.get("avgOnlineDuration"));
                response.setConnectionSuccessRate((Double) overallStats.get("connectionSuccessRate"));
                response.setTotalDataTransfer((Long) overallStats.get("totalDataTransfer"));
                response.setLastConnectedAt((Date) overallStats.get("lastConnectedAt"));
            }
            
            // 转换每日统计
            List<DeviceStatisticsResponseDTO.DailyStatistics> dailyStatisticsList = new ArrayList<>();
            for (DeviceUsageStatisticsEntity entity : dailyStats) {
                DeviceStatisticsResponseDTO.DailyStatistics daily = new DeviceStatisticsResponseDTO.DailyStatistics();
                daily.setDate(entity.getStatisticsDate());
                daily.setConnectionCount(entity.getConnectionCount());
                daily.setOnlineDuration(entity.getTotalOnlineDuration());
                daily.setSuccessConnectionCount(entity.getSuccessConnectionCount());
                daily.setFailureConnectionCount(entity.getFailureConnectionCount());
                daily.setDataTransferBytes(entity.getDataTransferBytes());
                dailyStatisticsList.add(daily);
            }
            response.setDailyStatistics(dailyStatisticsList);
            
            return response;
            
        } catch (ParseException e) {
            throw new RenException("日期格式错误");
        }
    }
    
    @Override
    public List<Map<String, Object>> getActiveDevicesRanking(String startDate, String endDate, Integer limit) {
        try {
            Date start = dateFormat.parse(startDate);
            Date end = dateFormat.parse(endDate);
            
            return deviceUsageStatisticsDao.getActiveDevicesRanking(start, end, limit);
            
        } catch (ParseException e) {
            throw new RenException("日期格式错误");
        }
    }
    
    @Override
    public void recordDeviceUsage(String deviceId, String macAddress, Long userId, String agentId, 
                                 Long onlineDuration, Boolean isSuccess, Long dataTransferBytes) {
        Long tenantId = TenantContext.getTenantId();
        
        deviceUsageStatisticsDao.recordDeviceUsage(deviceId, macAddress, userId, tenantId, agentId, 
                                                  onlineDuration, isSuccess, dataTransferBytes);
    }
    
    @Override
    public Map<String, Object> getTenantDeviceOverview(Long tenantId) {
        // 获取最近30天的统计
        Date endDate = new Date();
        Date startDate = new Date(endDate.getTime() - 30L * 24 * 60 * 60 * 1000);
        
        return deviceUsageStatisticsDao.getTenantDeviceStatistics(tenantId, startDate, endDate);
    }
    
    @Override
    public Map<String, Object> getDeviceOverallStatistics(String deviceId, String startDate, String endDate) {
        try {
            Date start = dateFormat.parse(startDate);
            Date end = dateFormat.parse(endDate);
            
            return deviceUsageStatisticsDao.getDeviceOverallStatistics(deviceId, start, end);
            
        } catch (ParseException e) {
            throw new RenException("日期格式错误");
        }
    }
    
    @Override
    public List<DeviceUsageStatisticsEntity> getUserDeviceStatistics(Long userId, String startDate, String endDate) {
        try {
            Date start = dateFormat.parse(startDate);
            Date end = dateFormat.parse(endDate);
            
            return deviceUsageStatisticsDao.getStatisticsByUserAndDateRange(userId, start, end);
            
        } catch (ParseException e) {
            throw new RenException("日期格式错误");
        }
    }
    
    @Override
    public List<DeviceUsageStatisticsEntity> getAgentDeviceStatistics(String agentId, String startDate, String endDate) {
        try {
            Date start = dateFormat.parse(startDate);
            Date end = dateFormat.parse(endDate);
            
            return deviceUsageStatisticsDao.getStatisticsByAgentAndDateRange(agentId, start, end);
            
        } catch (ParseException e) {
            throw new RenException("日期格式错误");
        }
    }
}
