package xiaozhi.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.PageData;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.sys.dao.SysOrganizationDao;
import xiaozhi.modules.sys.dao.SysTenantDao;
import xiaozhi.modules.sys.dto.TenantCreateRequestDTO;
import xiaozhi.modules.sys.dto.TenantDetailResponseDTO;
import xiaozhi.modules.sys.dto.TenantQueryRequestDTO;
import xiaozhi.modules.sys.dto.TenantUpdateRequestDTO;
import xiaozhi.modules.sys.entity.SysOrganizationEntity;
import xiaozhi.modules.sys.entity.SysTenantEntity;
import xiaozhi.modules.sys.entity.SysUserEntity;
import xiaozhi.modules.sys.service.SysTenantService;
import xiaozhi.modules.sys.service.SysUserService;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.extension.toolkit.Db.removeById;

/**
 * 租户Service实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class SysTenantServiceImpl extends BaseServiceImpl<SysTenantDao, SysTenantEntity> implements SysTenantService {

    private final SysTenantDao sysTenantDao;
    private final SysOrganizationDao sysOrganizationDao;
    private final SysUserService sysUserService;
    
    @Override
    public SysTenantEntity getByTenantCode(String tenantCode) {
        return sysTenantDao.getByTenantCode(tenantCode);
    }
    
    @Override
    public List<SysTenantEntity> getTenantsByOrgId(Long orgId) {
        return sysTenantDao.getTenantsByOrgId(orgId);
    }
    
    @Override
    public List<SysTenantEntity> getExpiringTenants(Integer days) {
        return sysTenantDao.getExpiringTenants(days);
    }

    @Override
    public PageData<TenantDetailResponseDTO> pageWithDetails(TenantQueryRequestDTO queryRequest) {
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.PAGE, queryRequest.getPage());
        params.put(Constant.LIMIT, queryRequest.getLimit());

        QueryWrapper<SysTenantEntity> wrapper = buildQueryWrapper(queryRequest);

        IPage<SysTenantEntity> page = baseDao.selectPage(
            getPage(params, "create_date", false),
            wrapper
        );

        List<TenantDetailResponseDTO> list = page.getRecords().stream()
            .map(this::convertToTenantDetail)
            .toList();

        return new PageData<>(list, page.getTotal());
    }

    @Override
    public TenantDetailResponseDTO getTenantDetail(Long tenantId) {
        SysTenantEntity entity = baseDao.selectById(tenantId);
        if (entity == null) {
            return null;
        }
        return convertToTenantDetail(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTenant(TenantCreateRequestDTO createRequest) {
        // 检查租户编码是否已存在
        if (existsTenantCode(createRequest.getTenantCode(), null)) {
            throw new RenException("租户编码已存在");
        }

        // 检查企业是否存在
        SysOrganizationEntity org = sysOrganizationDao.selectById(createRequest.getOrgId());
        if (org == null) {
            throw new RenException("企业不存在");
        }

        SysTenantEntity entity = new SysTenantEntity();
        entity.setTenantCode(createRequest.getTenantCode());
        entity.setTenantName(createRequest.getTenantName());
        entity.setOrgId(createRequest.getOrgId());
        entity.setAdminUserId(createRequest.getAdminUserId());
        entity.setMaxUsers(createRequest.getMaxUsers());
        entity.setMaxDevices(createRequest.getMaxDevices());
        entity.setMaxAgents(createRequest.getMaxAgents());
        entity.setStatus(createRequest.getStatus());
        entity.setExpireDate(createRequest.getExpireDate());
        entity.setRemark(createRequest.getRemark());

        insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenant(TenantUpdateRequestDTO updateRequest) {
        SysTenantEntity entity = baseDao.selectById(updateRequest.getId());
        if (entity == null) {
            throw new RenException("租户不存在");
        }

        // 检查租户编码是否被其他租户使用
        if (existsTenantCode(updateRequest.getTenantCode(), updateRequest.getId())) {
            throw new RenException("租户编码已被其他租户使用");
        }

        // 检查企业是否存在
        SysOrganizationEntity org = sysOrganizationDao.selectById(updateRequest.getOrgId());
        if (org == null) {
            throw new RenException("企业不存在");
        }

        entity.setTenantCode(updateRequest.getTenantCode());
        entity.setTenantName(updateRequest.getTenantName());
        entity.setOrgId(updateRequest.getOrgId());
        entity.setAdminUserId(updateRequest.getAdminUserId());
        entity.setMaxUsers(updateRequest.getMaxUsers());
        entity.setMaxDevices(updateRequest.getMaxDevices());
        entity.setMaxAgents(updateRequest.getMaxAgents());
        entity.setStatus(updateRequest.getStatus());
        entity.setExpireDate(updateRequest.getExpireDate());
        entity.setRemark(updateRequest.getRemark());

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTenant(Long tenantId) {
        SysTenantEntity entity = baseDao.selectById(tenantId);
        if (entity == null) {
            throw new RenException("租户不存在");
        }

        // 检查是否有关联的用户
        QueryWrapper<SysUserEntity> userWrapper = new QueryWrapper<>();
        userWrapper.eq("tenant_id", tenantId);
        userWrapper.eq("deleted", 0);
        long userCount = sysUserService.count(userWrapper);
        if (userCount > 0) {
            throw new RenException("租户下还有用户，无法删除");
        }

        // 软删除租户
        removeById(tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantStatus(Long tenantId, Integer status) {
        SysTenantEntity entity = new SysTenantEntity();
        entity.setId(tenantId);
        entity.setStatus(status);
        updateById(entity);
    }

    @Override
    public boolean existsTenantCode(String tenantCode, Long excludeId) {
        QueryWrapper<SysTenantEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("tenant_code", tenantCode);
        wrapper.eq("deleted", 0);
        if (excludeId != null) {
            wrapper.ne("id", excludeId);
        }
        return count(wrapper) > 0;
    }

    @Override
    public Map<String, Object> getTenantStatistics(Long tenantId) {
        Map<String, Object> statistics = new HashMap<>();

        // 获取用户数量
        QueryWrapper<SysUserEntity> userWrapper = new QueryWrapper<>();
        userWrapper.eq("tenant_id", tenantId);
        userWrapper.eq("deleted", 0);
        Long userCount = sysUserService.count(userWrapper);
        statistics.put("userCount", userCount);

        // TODO: 添加设备数量和智能体数量统计
        // 这里需要根据实际的设备和智能体服务来实现
        statistics.put("deviceCount", 0L);
        statistics.put("agentCount", 0L);

        return statistics;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<SysTenantEntity> buildQueryWrapper(TenantQueryRequestDTO queryRequest) {
        QueryWrapper<SysTenantEntity> wrapper = new QueryWrapper<>();

        // 租户编码模糊查询
        if (StringUtils.isNotBlank(queryRequest.getTenantCode())) {
            wrapper.like("tenant_code", queryRequest.getTenantCode());
        }

        // 租户名称模糊查询
        if (StringUtils.isNotBlank(queryRequest.getTenantName())) {
            wrapper.like("tenant_name", queryRequest.getTenantName());
        }

        // 企业ID精确查询
        if (queryRequest.getOrgId() != null) {
            wrapper.eq("org_id", queryRequest.getOrgId());
        }

        // 状态精确查询
        if (queryRequest.getStatus() != null) {
            wrapper.eq("status", queryRequest.getStatus());
        }

        // 创建时间范围查询
        if (StringUtils.isNotBlank(queryRequest.getCreateDateStart())) {
            wrapper.ge("create_date", queryRequest.getCreateDateStart());
        }
        if (StringUtils.isNotBlank(queryRequest.getCreateDateEnd())) {
            wrapper.le("create_date", queryRequest.getCreateDateEnd());
        }

        // 到期时间范围查询
        if (StringUtils.isNotBlank(queryRequest.getExpireDateStart())) {
            wrapper.ge("expire_date", queryRequest.getExpireDateStart());
        }
        if (StringUtils.isNotBlank(queryRequest.getExpireDateEnd())) {
            wrapper.le("expire_date", queryRequest.getExpireDateEnd());
        }

        // 排除已删除的记录
        wrapper.eq("deleted", 0);

        // 默认按创建时间倒序排列
        wrapper.orderByDesc("create_date");

        return wrapper;
    }

    /**
     * 转换为租户详情DTO
     */
    private TenantDetailResponseDTO convertToTenantDetail(SysTenantEntity entity) {
        TenantDetailResponseDTO dto = ConvertUtils.sourceToTarget(entity, TenantDetailResponseDTO.class);

        // 设置企业名称
        if (entity.getOrgId() != null) {
            SysOrganizationEntity org = sysOrganizationDao.selectById(entity.getOrgId());
            if (org != null) {
                dto.setOrgName(org.getOrgName());
            }
        }

        // 设置管理员信息
        if (entity.getAdminUserId() != null) {
            SysUserEntity adminUser = sysUserService.getById(entity.getAdminUserId());
            if (adminUser != null) {
                dto.setAdminUsername(adminUser.getUsername());
//                dto.setAdminRealName(adminUser.getRealName());
            }
        }

        // 设置状态名称
        dto.setStatusName(entity.getStatus() == 1 ? "启用" : "禁用");

        // 计算剩余天数和是否即将到期
        if (entity.getExpireDate() != null) {
            LocalDate expireDate = entity.getExpireDate().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate now = LocalDate.now();
            long remainingDays = ChronoUnit.DAYS.between(now, expireDate);

            dto.setRemainingDays((int) remainingDays);
            dto.setIsExpiring(remainingDays <= 30 && remainingDays >= 0);
        }

        // 获取统计信息
        Map<String, Object> statistics = getTenantStatistics(entity.getId());
        dto.setCurrentUsers((Long) statistics.get("userCount"));
        dto.setCurrentDevices((Long) statistics.get("deviceCount"));
        dto.setCurrentAgents((Long) statistics.get("agentCount"));

        return dto;
    }
}
