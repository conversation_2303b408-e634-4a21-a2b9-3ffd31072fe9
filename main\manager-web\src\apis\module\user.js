import { getServiceUrl } from '../api'
import RequestService from '../httpRequest'


export default {
    // 登录 - 更新为新的认证接口
    login(loginForm, callback, failCallback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/auth/login`)
            .method('POST')
            .data(loginForm)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .fail((err) => {
                RequestService.clearRequestTime()
                failCallback(err)
            })
            .networkFail(() => {
                RequestService.reAjaxFun(() => {
                    this.login(loginForm, callback)
                })
            }).send()
    },
    // 获取验证码 - 更新为新的认证接口
    getCaptcha(uuid, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/auth/captcha?uuid=${uuid}`)
            .method('GET')
            .type('blob')
            .header({
                'Content-Type': 'image/gif',
                'Pragma': 'No-cache',
                'Cache-Control': 'no-cache'
            })
            .success((res) => {
                RequestService.clearRequestTime();
                callback(res);
            })
            .networkFail((err) => {  // 添加错误参数

            }).send()
    },
    // 发送短信验证码 - 更新为新的认证接口
    sendSmsVerification(data, callback, failCallback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/auth/smsVerification`)
            .method('POST')
            .data(data)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .fail((err) => {
                RequestService.clearRequestTime()
                failCallback(err)
            })
            .networkFail(() => {
                RequestService.reAjaxFun(() => {
                    this.sendSmsVerification(data, callback, failCallback)
                })
            }).send()
    },
    // 注册账号 - 更新为新的认证接口
    register(registerForm, callback, failCallback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/auth/register`)
            .method('POST')
            .data(registerForm)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .fail((err) => {
                RequestService.clearRequestTime()
                failCallback(err)
            })
            .networkFail(() => {
                RequestService.reAjaxFun(() => {
                    this.register(registerForm, callback, failCallback)
                })
            }).send()
    },
    // 保存设备配置
    saveDeviceConfig(device_id, configData, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user/configDevice/${device_id}`)
            .method('PUT')
            .data(configData)
            .success((res) => {
                RequestService.clearRequestTime();
                callback(res);
            })
            .networkFail((err) => {
                console.error('保存配置失败:', err);
                RequestService.reAjaxFun(() => {
                    this.saveDeviceConfig(device_id, configData, callback);
                });
            }).send();
    },
    // 用户信息获取 - 更新为新的认证接口
    getUserInfo(callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/auth/info`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('接口请求失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getUserInfo(callback)
                })
            }).send()
    },
    // 修改用户密码 - 更新为新的认证接口
    changePassword(oldPassword, newPassword, successCallback, errorCallback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/auth/change-password`)
            .method('PUT')
            .data({
                password: oldPassword,
                newPassword: newPassword,
            })
            .success((res) => {
                RequestService.clearRequestTime();
                successCallback(res);
            })
            .networkFail((error) => {
                RequestService.reAjaxFun(() => {
                    this.changePassword(oldPassword, newPassword, successCallback, errorCallback);
                });
            })
            .send();
    },
    // 获取公共配置 - 更新为新的认证接口
    getPubConfig(callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/auth/pub-config`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime();
                callback(res);
            })
            .networkFail((err) => {
                console.error('获取公共配置失败:', err);
                RequestService.reAjaxFun(() => {
                    this.getPubConfig(callback);
                });
            }).send();
    },
    // 找回用户密码 - 更新为新的认证接口
    retrievePassword(passwordData, callback, failCallback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/auth/retrieve-password`)
            .method('PUT')
            .data({
                phone: passwordData.phone,
                code: passwordData.code,
                password: passwordData.password
            })
            .success((res) => {
                RequestService.clearRequestTime();
                callback(res);
            })
            .fail((err) => {
                RequestService.clearRequestTime();
                failCallback(err);
            })
            .networkFail(() => {
                RequestService.reAjaxFun(() => {
                    this.retrievePassword(passwordData, callback, failCallback);
                });
            }).send()
    },

    // 新增用户管理相关API
    // 用户分页查询
    getUserPage(params, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user/page`)
            .method('POST')
            .data(params)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('用户分页查询失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getUserPage(params, callback)
                })
            }).send()
    },

    // 获取用户列表
    getUserList(callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user/list`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取用户列表失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getUserList(callback)
                })
            }).send()
    },

    // 获取用户详情
    getUserDetail(userId, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user/${userId}`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取用户详情失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getUserDetail(userId, callback)
                })
            }).send()
    },

    // 创建用户
    createUser(data, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user`)
            .method('POST')
            .data(data)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('创建用户失败:', err)
                RequestService.reAjaxFun(() => {
                    this.createUser(data, callback)
                })
            }).send()
    },

    // 更新用户
    updateUser(data, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user`)
            .method('PUT')
            .data(data)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('更新用户失败:', err)
                RequestService.reAjaxFun(() => {
                    this.updateUser(data, callback)
                })
            }).send()
    },

    // 删除用户
    deleteUser(userId, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user/${userId}`)
            .method('DELETE')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('删除用户失败:', err)
                RequestService.reAjaxFun(() => {
                    this.deleteUser(userId, callback)
                })
            }).send()
    },

    // 修改用户状态 - 更新为新的用户管理接口
    changeUserStatus(userId, status, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user/${userId}/status`)
            .method('PUT')
            .data({ status: status })
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res);
            })
            .networkFail((err) => {
                console.error('修改用户状态失败:', err)
                RequestService.reAjaxFun(() => {
                    this.changeUserStatus(userId, status, callback)
                })
            }).send()
    },

    // 重置用户密码
    resetUserPassword(userId, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user/${userId}/reset-password`)
            .method('PUT')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('重置用户密码失败:', err)
                RequestService.reAjaxFun(() => {
                    this.resetUserPassword(userId, callback)
                })
            }).send()
    },

    // 获取用户角色
    getUserRoles(userId, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user/${userId}/roles`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取用户角色失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getUserRoles(userId, callback)
                })
            }).send()
    },

    // 获取用户权限
    getUserPermissions(userId, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user/${userId}/permissions`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取用户权限失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getUserPermissions(userId, callback)
                })
            }).send()
    }
}
