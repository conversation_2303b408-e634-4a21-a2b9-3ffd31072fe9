import { getServiceUrl } from '../api';
import RequestService from '../httpRequest';

export default {
    // 租户分页查询
    getTenantPage(params, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/tenant/page`)
            .method('POST')
            .data(params)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('租户分页查询失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getTenantPage(params, callback)
                })
            }).send()
    },

    // 创建租户
    createTenant(data, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/tenant`)
            .method('POST')
            .data(data)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('创建租户失败:', err)
                RequestService.reAjaxFun(() => {
                    this.createTenant(data, callback)
                })
            }).send()
    },

    // 更新租户
    updateTenant(data, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/tenant`)
            .method('PUT')
            .data(data)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('更新租户失败:', err)
                RequestService.reAjaxFun(() => {
                    this.updateTenant(data, callback)
                })
            }).send()
    },

    // 删除租户
    deleteTenant(id, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/tenant/${id}`)
            .method('DELETE')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('删除租户失败:', err)
                RequestService.reAjaxFun(() => {
                    this.deleteTenant(id, callback)
                })
            }).send()
    },

    // 获取租户详情
    getTenantDetail(id, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/tenant/detail/${id}`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取租户详情失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getTenantDetail(id, callback)
                })
            }).send()
    },

    // 获取租户统计
    getTenantStatistics(id, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/sys/tenant/${id}/statistics`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取租户统计失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getTenantStatistics(id, callback)
                })
            }).send()
    }
}
