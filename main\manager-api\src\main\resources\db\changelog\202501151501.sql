-- 多租户改造初始数据脚本
-- 版本: 202501151501
-- 作者: AI Assistant
-- 描述: 初始化多租户系统的基础数据，包括默认企业、租户和管理员用户

-- =====================================================
-- 1. 初始化默认企业
-- =====================================================
INSERT INTO `sys_organization` (
    `id`, `org_code`, `org_name`, `org_type`, `contact_person`, 
    `contact_phone`, `contact_email`, `address`, `status`, 
    `creator`, `created_at`, `updater`, `updated_at`, `deleted`
) VALUES (
    1, 'DEFAULT_ORG', '默认企业', 1, '系统管理员',
    '************', '<EMAIL>', '北京市海淀区', 1,
    1, NOW(), 1, NOW(), 0
);

-- =====================================================
-- 2. 初始化默认租户
-- =====================================================
INSERT INTO `sys_tenant` (
    `id`, `tenant_code`, `tenant_name`, `org_id`, `admin_user_id`,
    `max_users`, `max_devices`, `max_agents`, `status`, 
    `creator`, `created_at`, `updater`, `updated_at`, `deleted`
) VALUES (
    1, 'DEFAULT_TENANT', '默认租户', 1, 1,
    1000, 10000, 100, 1,
    1, NOW(), 1, NOW(), 0
);

-- =====================================================
-- 3. 更新现有用户数据
-- =====================================================
-- 将现有的超级管理员设置为平台管理员
UPDATE `sys_user` 
SET 
    `tenant_id` = 1,
    `user_type` = 1,
    `real_name` = COALESCE(`username`, '系统管理员'),
    `deleted` = 0
WHERE `super_admin` = 1;

-- 将其他用户设置为普通用户
UPDATE `sys_user` 
SET 
    `tenant_id` = 1,
    `user_type` = 3,
    `real_name` = COALESCE(`username`, '普通用户'),
    `deleted` = 0
WHERE `super_admin` != 1 OR `super_admin` IS NULL;

-- =====================================================
-- 4. 更新现有智能体数据
-- =====================================================
-- 将现有智能体分配给默认租户
UPDATE `ai_agent` 
SET 
    `tenant_id` = 1,
    `deleted` = 0
WHERE `tenant_id` IS NULL;

-- =====================================================
-- 5. 更新现有设备数据
-- =====================================================
-- 将现有设备分配给默认租户
UPDATE `ai_device` 
SET 
    `tenant_id` = 1,
    `deleted` = 0
WHERE `tenant_id` IS NULL;

-- =====================================================
-- 6. 更新租户管理员关联
-- =====================================================
-- 更新默认租户的管理员用户ID
UPDATE `sys_tenant` 
SET `admin_user_id` = (
    SELECT `id` FROM `sys_user` 
    WHERE `user_type` = 1 AND `tenant_id` = 1 
    LIMIT 1
)
WHERE `id` = 1;

-- =====================================================
-- 7. 创建智能体分配记录（为现有数据）
-- =====================================================
-- 为现有的智能体-用户关系创建分配记录
INSERT INTO `ai_agent_assignment` (
    `agent_id`, `user_id`, `tenant_id`, `status`, 
    `assigned_at`, `assigned_by`, `creator`, `created_at`, 
    `updater`, `updated_at`, `deleted`
)
SELECT 
    a.`id` as `agent_id`,
    a.`user_id`,
    a.`tenant_id`,
    1 as `status`,
    a.`create_date` as `assigned_at`,
    a.`creator` as `assigned_by`,
    a.`creator`,
    a.`create_date` as `created_at`,
    a.`updater`,
    a.`update_date` as `updated_at`,
    0 as `deleted`
FROM `ai_agent` a
WHERE a.`user_id` IS NOT NULL 
  AND a.`tenant_id` IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM `ai_agent_assignment` aa 
      WHERE aa.`agent_id` = a.`id` AND aa.`user_id` = a.`user_id`
  );

-- =====================================================
-- 8. 初始化统计数据（可选）
-- =====================================================
-- 为现有智能体创建初始统计记录
INSERT INTO `ai_agent_usage_statistics` (
    `agent_id`, `user_id`, `tenant_id`, `statistics_date`,
    `usage_count`, `total_duration`, `success_count`, 
    `created_at`, `updated_at`
)
SELECT 
    a.`id` as `agent_id`,
    a.`user_id`,
    a.`tenant_id`,
    CURDATE() as `statistics_date`,
    0 as `usage_count`,
    0 as `total_duration`,
    0 as `success_count`,
    NOW() as `created_at`,
    NOW() as `updated_at`
FROM `ai_agent` a
WHERE a.`user_id` IS NOT NULL 
  AND a.`tenant_id` IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM `ai_agent_usage_statistics` aus 
      WHERE aus.`agent_id` = a.`id` 
        AND aus.`user_id` = a.`user_id` 
        AND aus.`statistics_date` = CURDATE()
  );

-- 为现有设备创建初始统计记录
INSERT INTO `ai_device_usage_statistics` (
    `device_id`, `mac_address`, `user_id`, `tenant_id`, `agent_id`,
    `statistics_date`, `connection_count`, `total_online_duration`,
    `success_connection_count`, `created_at`, `updated_at`
)
SELECT 
    d.`id` as `device_id`,
    d.`mac_address`,
    d.`user_id`,
    d.`tenant_id`,
    d.`agent_id`,
    CURDATE() as `statistics_date`,
    0 as `connection_count`,
    0 as `total_online_duration`,
    0 as `success_connection_count`,
    NOW() as `created_at`,
    NOW() as `updated_at`
FROM `ai_device` d
WHERE d.`user_id` IS NOT NULL 
  AND d.`tenant_id` IS NOT NULL
  AND d.`mac_address` IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM `ai_device_usage_statistics` dus 
      WHERE dus.`device_id` = d.`id` 
        AND dus.`user_id` = d.`user_id` 
        AND dus.`statistics_date` = CURDATE()
  );

-- =====================================================
-- 9. 数据完整性检查
-- =====================================================
-- 检查是否有用户没有分配租户
SELECT COUNT(*) as users_without_tenant 
FROM `sys_user` 
WHERE `tenant_id` IS NULL;

-- 检查是否有智能体没有分配租户
SELECT COUNT(*) as agents_without_tenant 
FROM `ai_agent` 
WHERE `tenant_id` IS NULL;

-- 检查是否有设备没有分配租户
SELECT COUNT(*) as devices_without_tenant 
FROM `ai_device` 
WHERE `tenant_id` IS NULL;

-- =====================================================
-- 10. 创建视图（可选）
-- =====================================================
-- 创建租户统计视图
CREATE OR REPLACE VIEW `v_tenant_statistics` AS
SELECT 
    t.`id` as `tenant_id`,
    t.`tenant_name`,
    t.`org_id`,
    o.`org_name`,
    COUNT(DISTINCT u.`id`) as `user_count`,
    COUNT(DISTINCT a.`id`) as `agent_count`,
    COUNT(DISTINCT d.`id`) as `device_count`,
    t.`max_users`,
    t.`max_agents`,
    t.`max_devices`,
    t.`status`,
    t.`expire_date`
FROM `sys_tenant` t
LEFT JOIN `sys_organization` o ON t.`org_id` = o.`id`
LEFT JOIN `sys_user` u ON t.`id` = u.`tenant_id` AND u.`deleted` = 0
LEFT JOIN `ai_agent` a ON t.`id` = a.`tenant_id` AND a.`deleted` = 0
LEFT JOIN `ai_device` d ON t.`id` = d.`tenant_id` AND d.`deleted` = 0
WHERE t.`deleted` = 0
GROUP BY t.`id`, t.`tenant_name`, t.`org_id`, o.`org_name`, 
         t.`max_users`, t.`max_agents`, t.`max_devices`, 
         t.`status`, t.`expire_date`;

-- =====================================================
-- 11. 索引优化建议
-- =====================================================
-- 为提高查询性能，建议创建以下复合索引

-- 用户表复合索引
-- ALTER TABLE `sys_user` ADD INDEX `idx_user_tenant_type` (`tenant_id`, `user_type`);

-- 智能体表复合索引
-- ALTER TABLE `ai_agent` ADD INDEX `idx_agent_tenant_user` (`tenant_id`, `user_id`);

-- 设备表复合索引
-- ALTER TABLE `ai_device` ADD INDEX `idx_device_tenant_user` (`tenant_id`, `user_id`);

-- 分配表复合索引
-- ALTER TABLE `ai_agent_assignment` ADD INDEX `idx_assignment_tenant_status` (`tenant_id`, `status`);

-- 统计表复合索引
-- ALTER TABLE `ai_agent_usage_statistics` ADD INDEX `idx_agent_stats_tenant_date` (`tenant_id`, `statistics_date`);
-- ALTER TABLE `ai_device_usage_statistics` ADD INDEX `idx_device_stats_tenant_date` (`tenant_id`, `statistics_date`);
