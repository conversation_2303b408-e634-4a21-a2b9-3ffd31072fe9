package xiaozhi.modules.sys.service.impl;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.sys.dao.SysRoleDao;
import xiaozhi.modules.sys.entity.SysRoleEntity;
import xiaozhi.modules.sys.service.SysRoleService;

/**
 * 角色Service实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class SysRoleServiceImpl extends BaseServiceImpl<SysRoleDao, SysRoleEntity> implements SysRoleService {
    
    private final SysRoleDao sysRoleDao;
    
    @Override
    public Set<String> getUserRoles(Long userId) {
        return sysRoleDao.getUserRoles(userId);
    }
    
    @Override
    public List<SysRoleEntity> getRolesByTenantId(Long tenantId) {
        return sysRoleDao.getRolesByTenantId(tenantId);
    }
    
    @Override
    public List<SysRoleEntity> getRolesByType(Integer roleType) {
        return sysRoleDao.getRolesByType(roleType);
    }
}
