package xiaozhi.modules.agent.controller;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.agent.dto.AgentStatisticsQueryDTO;
import xiaozhi.modules.agent.dto.AgentStatisticsResponseDTO;
import xiaozhi.modules.agent.service.AgentService;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.tenant.TenantContext;

import javax.validation.Valid;

/**
 * 智能体统计Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@RestController
@RequestMapping("/agent/statistics")
@Tag(name = "智能体统计", description = "智能体使用统计和分析")
public class AgentStatisticsController {
    
    private final AgentService agentService;
    
    @PostMapping("/query")
    @Operation(
        operationId = "getAgentStatistics",
        summary = "获取智能体使用统计",
        description = "根据查询条件获取智能体的使用统计数据，包括使用次数、时长、成功率等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "agent:statistics:*", description = "查看智能体统计")
    public Result<AgentStatisticsResponseDTO> getAgentStatistics(
            @Parameter(description = "统计查询条件", required = true)
            @RequestBody @Valid AgentStatisticsQueryDTO queryDTO) {
        AgentStatisticsResponseDTO statistics = agentService.getAgentStatistics(queryDTO);
        return new Result<AgentStatisticsResponseDTO>().ok(statistics);
    }
    
    @GetMapping("/popular")
    @Operation(
        operationId = "getPopularAgentsRanking",
        summary = "获取热门智能体排行",
        description = "获取指定时间范围内的热门智能体排行榜"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "agent:statistics:*", description = "查看热门智能体")
    public Result<List<Map<String, Object>>> getPopularAgentsRanking(
            @Parameter(description = "开始日期", required = true, example = "2025-01-01")
            @RequestParam String startDate,
            @Parameter(description = "结束日期", required = true, example = "2025-01-31")
            @RequestParam String endDate,
            @Parameter(description = "限制数量", example = "10", 
                schema = @Schema(minimum = "1", maximum = "100"))
            @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> ranking = agentService.getPopularAgentsRanking(startDate, endDate, limit);
        return new Result<List<Map<String, Object>>>().ok(ranking);
    }
    
    @PostMapping("/usage")
    @Operation(
        operationId = "recordAgentUsage",
        summary = "记录智能体使用情况",
        description = "记录智能体的使用情况，用于统计分析"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "记录成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "agent:usage:*", description = "记录智能体使用")
    public Result<Void> recordAgentUsage(
            @Parameter(description = "智能体ID", required = true, example = "agent123")
            @RequestParam String agentId,
            @Parameter(description = "用户ID", required = true, example = "1")
            @RequestParam Long userId,
            @Parameter(description = "使用时长（秒）", required = true, example = "60")
            @RequestParam Long duration,
            @Parameter(description = "是否成功", required = true, example = "true")
            @RequestParam Boolean isSuccess) {
        agentService.recordAgentUsage(agentId, userId, duration, isSuccess);
        return new Result<>();
    }
    
    @GetMapping("/tenant/overview")
    @Operation(
        operationId = "getTenantAgentOverview",
        summary = "获取租户智能体统计概览",
        description = "获取当前租户的智能体使用统计概览"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "agent:statistics:*", description = "查看租户智能体概览")
    public Result<Map<String, Object>> getTenantAgentOverview() {
        Long tenantId = TenantContext.getTenantId();
        Map<String, Object> overview = agentService.getTenantAgentOverview(tenantId);
        return new Result<Map<String, Object>>().ok(overview);
    }
    
    @GetMapping("/tenant/{tenantId}/overview")
    @Operation(
        operationId = "getTenantAgentOverviewById",
        summary = "获取指定租户智能体统计概览",
        description = "获取指定租户的智能体使用统计概览（仅平台管理员可用）"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    @RequiresPermission(value = "agent:statistics:platform", description = "查看所有租户智能体概览")
    public Result<Map<String, Object>> getTenantAgentOverviewById(
            @Parameter(description = "租户ID", required = true, example = "1")
            @PathVariable Long tenantId) {
        Map<String, Object> overview = agentService.getTenantAgentOverview(tenantId);
        return new Result<Map<String, Object>>().ok(overview);
    }
}
