package xiaozhi.modules.sys.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import xiaozhi.common.dao.BaseDao;
import xiaozhi.modules.sys.entity.SysUserRoleEntity;

/**
 * 用户角色关联DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Mapper
public interface SysUserRoleDao extends BaseDao<SysUserRoleEntity> {
    
    /**
     * 根据用户ID删除角色关联
     * 
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID删除用户关联
     * 
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 批量插入用户角色关联
     * 
     * @param userRoles 用户角色关联列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<SysUserRoleEntity> userRoles);
    
    /**
     * 根据用户ID获取角色ID列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getRoleIdsByUserId(@Param("userId") Long userId);
}
