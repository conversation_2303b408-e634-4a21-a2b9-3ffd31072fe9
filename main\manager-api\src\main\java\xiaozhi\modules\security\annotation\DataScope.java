package xiaozhi.modules.security.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限注解
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope {
    
    /**
     * 表别名
     */
    String tableAlias() default "";
    
    /**
     * 租户字段名
     */
    String tenantColumn() default "tenant_id";
    
    /**
     * 用户字段名
     */
    String userColumn() default "user_id";
    
    /**
     * 是否启用租户过滤
     */
    boolean tenantFilter() default true;
    
    /**
     * 是否启用用户过滤
     */
    boolean userFilter() default false;
}
