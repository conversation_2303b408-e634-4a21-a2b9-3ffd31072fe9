# 设备记忆功能使用指南

## 功能概述

在设备管理页面的列表中新增了"设备记忆"字段，用于展示每个设备的记忆信息。该功能支持：

1. **固定长度显示**：表格中显示记忆内容的前50个字符，超出部分用省略号（...）替代
2. **Tooltip提示**：鼠标悬停在记忆内容上时，显示完整的记忆信息
3. **点击查看详情**：点击记忆内容可以打开弹窗查看完整记忆信息
4. **无记忆状态**：当设备没有记忆时，显示"暂无记忆"

## 功能实现

### 前端实现

#### 1. API接口
在 `src/apis/module/device.js` 中添加了获取设备记忆的接口：

```javascript
// 获取设备记忆
getDeviceMemory(macAddress, callback) {
    RequestService.sendRequest()
        .url(`${getServiceUrl()}/device/getDeviceMemory/${macAddress}`)
        .method('GET')
        .success((res) => {
            RequestService.clearRequestTime();
            callback(res);
        })
        .networkFail((err) => {
            console.error('获取设备记忆失败:', err);
            RequestService.reAjaxFun(() => {
                this.getDeviceMemory(macAddress, callback);
            });
        }).send();
},
```

#### 2. 页面组件
在 `src/views/DeviceManagement.vue` 中添加了记忆列：

```vue
<el-table-column label="设备记忆" align="center" width="200">
  <template slot-scope="scope">
    <div class="memory-cell">
      <el-tooltip 
        v-if="scope.row.summaryMemory" 
        :content="scope.row.summaryMemory" 
        placement="top" 
        effect="dark"
        :open-delay="300">
        <span 
          class="memory-text" 
          @click="showFullMemory(scope.row.summaryMemory)">
          {{ truncateMemory(scope.row.summaryMemory) }}
        </span>
      </el-tooltip>
      <span v-else class="no-memory">暂无记忆</span>
    </div>
  </template>
</el-table-column>
```

#### 3. 核心方法

```javascript
// 截断记忆文本用于表格显示
truncateMemory(memory) {
  if (!memory) return '';
  if (memory.length <= this.memoryMaxLength) {
    return memory;
  }
  return memory.substring(0, this.memoryMaxLength) + '...';
},

// 显示完整记忆内容
showFullMemory(memory) {
  this.currentMemoryContent = memory || '';
  this.memoryDialogVisible = true;
},
```

### 后端支持

后端已经实现了完整的设备记忆功能：

1. **数据库字段**：在 `ai_device` 表中添加了 `summary_memory` 字段
2. **API接口**：
   - `GET /device/getDeviceMemory/{macAddress}` - 获取设备记忆
   - `PUT /device/saveDeviceMemory/{macAddress}` - 保存设备记忆
3. **数据返回**：设备列表接口已经包含了 `summaryMemory` 字段

## 使用方法

### 1. 查看设备记忆

1. 进入设备管理页面
2. 在设备列表中查看"设备记忆"列
3. 如果记忆内容较长，会显示前50个字符 + "..."
4. 鼠标悬停可以看到完整内容的tooltip
5. 点击记忆内容可以打开详情弹窗

### 2. 记忆内容的来源

设备记忆内容来自于：
- xiaozhi-server在连接超时时自动保存的对话记忆
- 通过API手动保存的记忆内容

### 3. 样式说明

- **有记忆**：显示为可点击的蓝色文本，悬停时有下划线
- **无记忆**：显示为灰色斜体的"暂无记忆"
- **Tooltip**：深色背景，最大宽度400px，支持换行显示
- **弹窗**：60%宽度，只读文本域，10行高度

## 测试组件

创建了测试组件 `src/components/DeviceMemoryTest.vue` 用于功能验证：

1. 测试记忆文本截断功能
2. 测试API调用
3. 测试弹窗显示

## 配置参数

可以通过修改以下参数来调整显示效果：

```javascript
data() {
  return {
    memoryMaxLength: 50, // 表格中显示的最大字符数
    // ...
  };
}
```

## 注意事项

1. **性能考虑**：记忆内容直接从设备列表接口获取，无需额外API调用
2. **字符截断**：按字符数截断，中英文都按1个字符计算
3. **兼容性**：支持没有记忆内容的设备，显示友好的提示信息
4. **响应式**：记忆列宽度固定为200px，适配不同屏幕尺寸

## 未来扩展

可以考虑的功能扩展：

1. **记忆编辑**：在前端直接编辑设备记忆
2. **记忆搜索**：支持按记忆内容搜索设备
3. **记忆统计**：显示记忆内容的统计信息
4. **记忆导出**：批量导出设备记忆信息
