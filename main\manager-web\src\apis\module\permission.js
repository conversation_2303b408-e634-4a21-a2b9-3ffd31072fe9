import { getServiceUrl } from '../api';
import RequestService from '../httpRequest';

export default {
    // 获取当前用户权限信息
    getCurrentUserPermissions(callback) {
        try {
            RequestService.sendRequest()
                .url(`${getServiceUrl()}/user-permission/current/info`)
                .method('GET')
                .success((res) => {
                    RequestService.clearRequestTime()
                    callback(res)
                })
                .fail((err) => {
                    console.warn('获取当前用户权限失败:', err)
                    // 返回默认权限结构
                    callback({
                        code: 0,
                        data: {
                            permissions: [],
                            roles: []
                        }
                    })
                })
                .networkFail((err) => {
                    console.error('获取当前用户权限网络失败:', err)
                    // 返回默认权限结构
                    callback({
                        code: 0,
                        data: {
                            permissions: [],
                            roles: []
                        }
                    })
                }).send()
        } catch (error) {
            console.error('获取当前用户权限异常:', error)
            callback({
                code: 0,
                data: {
                    permissions: [],
                    roles: []
                }
            })
        }
    },

    // 获取用户权限
    getUserPermissions(userId, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user-permission/user/${userId}/permissions`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取用户权限失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getUserPermissions(userId, callback)
                })
            }).send()
    },

    // 检查用户权限
    checkUserPermission(userId, permission, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user-permission/check/${userId}/${permission}`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('检查用户权限失败:', err)
                RequestService.reAjaxFun(() => {
                    this.checkUserPermission(userId, permission, callback)
                })
            }).send()
    },

    // 获取可用权限列表
    getAvailablePermissions(callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user-permission/available-permissions`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取可用权限失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getAvailablePermissions(callback)
                })
            }).send()
    },

    // 权限分析
    getPermissionAnalysis(callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user-permission/permission/analysis`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('权限分析失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getPermissionAnalysis(callback)
                })
            }).send()
    },

    // 获取用户角色
    getUserRoles(userId, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user-role/user/${userId}/roles`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取用户角色失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getUserRoles(userId, callback)
                })
            }).send()
    },

    // 获取角色用户
    getRoleUsers(roleId, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user-role/role/${roleId}/users`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取角色用户失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getRoleUsers(roleId, callback)
                })
            }).send()
    },

    // 分配角色
    assignRole(data, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user-role/assign`)
            .method('POST')
            .data(data)
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('分配角色失败:', err)
                RequestService.reAjaxFun(() => {
                    this.assignRole(data, callback)
                })
            }).send()
    },

    // 取消角色
    removeRole(userId, roleId, callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user-role/${userId}/${roleId}`)
            .method('DELETE')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('取消角色失败:', err)
                RequestService.reAjaxFun(() => {
                    this.removeRole(userId, roleId, callback)
                })
            }).send()
    },

    // 获取可分配角色
    getAvailableRoles(callback) {
        RequestService.sendRequest()
            .url(`${getServiceUrl()}/user-role/available-roles`)
            .method('GET')
            .success((res) => {
                RequestService.clearRequestTime()
                callback(res)
            })
            .networkFail((err) => {
                console.error('获取可分配角色失败:', err)
                RequestService.reAjaxFun(() => {
                    this.getAvailableRoles(callback)
                })
            }).send()
    }
}
