package xiaozhi.modules.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 字典数据项DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "字典数据项")
public class SysDictDataItem {
    
    @Schema(description = "字典标签", example = "男")
    private String dictLabel;
    
    @Schema(description = "字典值", example = "1")
    private String dictValue;
    
    @Schema(description = "字典类型", example = "gender")
    private String dictType;
    
    @Schema(description = "排序", example = "1")
    private Integer sort;
    
    @Schema(description = "备注", example = "性别-男")
    private String remark;
}
