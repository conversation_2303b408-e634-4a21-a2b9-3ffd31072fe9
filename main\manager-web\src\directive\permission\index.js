// 延迟导入权限函数以避免循环依赖
let permissionUtils = null
function getPermissionUtils() {
  if (!permissionUtils) {
    try {
      permissionUtils = require('@/utils/permission')
    } catch (error) {
      console.warn('权限工具函数加载失败:', error)
      // 提供默认的权限检查函数
      permissionUtils = {
        hasPermission: () => false,
        hasAnyPermission: () => false,
        hasAllPermissions: () => false
      }
    }
  }
  return permissionUtils
}

/**
 * v-permission 权限指令
 * 用法：
 * v-permission="'user:create:tenant'" - 单个权限
 * v-permission="['user:create:tenant', 'user:update:tenant']" - 多个权限（任意一个）
 * v-permission.all="['user:create:tenant', 'user:update:tenant']" - 多个权限（全部）
 */
export default {
  inserted(el, binding) {
    try {
      const { value, modifiers } = binding
      const { hasPermission, hasAnyPermission, hasAllPermissions } = getPermissionUtils()

      if (value) {
        let hasAuth = false

        if (Array.isArray(value)) {
          // 数组权限
          if (modifiers.all) {
            // 需要所有权限
            hasAuth = hasAllPermissions(value)
          } else {
            // 需要任意一个权限
            hasAuth = hasAnyPermission(value)
          }
        } else if (typeof value === 'string') {
          // 单个权限
          hasAuth = hasPermission(value)
        }

        if (!hasAuth) {
          el.parentNode && el.parentNode.removeChild(el)
        }
      } else {
        console.warn('权限指令需要传入权限字符串或权限数组')
      }
    } catch (error) {
      console.warn('权限指令执行失败:', error)
    }
  },

  update(el, binding) {
    try {
      // 当权限发生变化时重新检查
      const { value, modifiers } = binding
      const { hasPermission, hasAnyPermission, hasAllPermissions } = getPermissionUtils()

      if (value) {
        let hasAuth = false

        if (Array.isArray(value)) {
          if (modifiers.all) {
            hasAuth = hasAllPermissions(value)
          } else {
            hasAuth = hasAnyPermission(value)
          }
        } else if (typeof value === 'string') {
          hasAuth = hasPermission(value)
        }

        if (!hasAuth) {
          el.style.display = 'none'
        } else {
          el.style.display = ''
        }
      }
    } catch (error) {
      console.warn('权限指令更新失败:', error)
    }
  }
}

/**
 * v-permission-disabled 权限禁用指令
 * 没有权限时禁用元素而不是隐藏
 */
export const permissionDisabled = {
  inserted(el, binding) {
    try {
      const { value } = binding
      const { hasPermission, hasAnyPermission } = getPermissionUtils()

      if (value) {
        let hasAuth = false

        if (Array.isArray(value)) {
          hasAuth = hasAnyPermission(value)
        } else if (typeof value === 'string') {
          hasAuth = hasPermission(value)
        }

        if (!hasAuth) {
          el.disabled = true
          el.classList.add('is-disabled')
          el.setAttribute('title', '您没有权限执行此操作')
        }
      }
    } catch (error) {
      console.warn('权限禁用指令执行失败:', error)
    }
  },

  update(el, binding) {
    try {
      const { value } = binding
      const { hasPermission, hasAnyPermission } = getPermissionUtils()

      if (value) {
        let hasAuth = false

        if (Array.isArray(value)) {
          hasAuth = hasAnyPermission(value)
        } else if (typeof value === 'string') {
          hasAuth = hasPermission(value)
        }

        if (!hasAuth) {
          el.disabled = true
          el.classList.add('is-disabled')
          el.setAttribute('title', '您没有权限执行此操作')
        } else {
          el.disabled = false
          el.classList.remove('is-disabled')
          el.removeAttribute('title')
        }
      }
    } catch (error) {
      console.warn('权限禁用指令更新失败:', error)
    }
  }
}
