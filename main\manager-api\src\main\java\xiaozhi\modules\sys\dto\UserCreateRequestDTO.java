package xiaozhi.modules.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 用户创建请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "用户创建请求")
public class UserCreateRequestDTO {
    
    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "testuser")
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @Schema(description = "真实姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotBlank(message = "真实姓名不能为空")
    private String realName;
    
    @Schema(description = "头像URL", example = "http://example.com/avatar.jpg")
    private String headUrl;
    
    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @Range(min = 0, max = 2, message = "性别值必须在0-2之间")
    private Integer gender;
    
    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;
    
    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "用户类型不能为空")
    @Range(min = 1, max = 3, message = "用户类型值必须在1-3之间")
    private Integer userType;
    
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @Range(min = 0, max = 1, message = "状态值必须在0-1之间")
    private Integer status = 1;
    
    @Schema(description = "租户ID", example = "1")
    private Long tenantId;
    
    @Schema(description = "部门ID", example = "1")
    private Long deptId;
    
    @Schema(description = "角色ID列表", example = "[1, 2]")
    private List<Long> roleIdList;
    
    @Schema(description = "岗位ID列表", example = "[1, 2]")
    private List<Long> postIdList;
}
