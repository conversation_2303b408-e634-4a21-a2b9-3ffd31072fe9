-- 权限数据初始化脚本
-- 作者: AI Assistant
-- 日期: 2025-01-15 14:01

-- 1. 初始化权限数据

-- 1.1 企业管理权限
INSERT INTO `sys_permission` (`permission`, `description`, `domain`, `action`, `instance`, `creator`, `create_date`) VALUES
('organization:view:*', '查看所有企业', 'organization', 'view', '*', 1, NOW()),
('organization:create:*', '创建企业', 'organization', 'create', '*', 1, NOW()),
('organization:update:*', '编辑企业', 'organization', 'update', '*', 1, NOW()),
('organization:delete:*', '删除企业', 'organization', 'delete', '*', 1, NOW());

-- 1.2 租户管理权限
INSERT INTO `sys_permission` (`permission`, `description`, `domain`, `action`, `instance`, `creator`, `create_date`) VALUES
('tenant:view:*', '查看所有租户', 'tenant', 'view', '*', 1, NOW()),
('tenant:create:*', '创建租户', 'tenant', 'create', '*', 1, NOW()),
('tenant:update:*', '编辑租户', 'tenant', 'update', '*', 1, NOW()),
('tenant:delete:*', '删除租户', 'tenant', 'delete', '*', 1, NOW());

-- 1.3 用户管理权限
INSERT INTO `sys_permission` (`permission`, `description`, `domain`, `action`, `instance`, `creator`, `create_date`) VALUES
('user:view:*', '查看所有用户', 'user', 'view', '*', 1, NOW()),
('user:view:tenant', '查看租户内用户', 'user', 'view', 'tenant', 1, NOW()),
('user:create:*', '创建任意用户', 'user', 'create', '*', 1, NOW()),
('user:create:tenant', '创建租户用户', 'user', 'create', 'tenant', 1, NOW()),
('user:update:*', '编辑任意用户', 'user', 'update', '*', 1, NOW()),
('user:update:tenant', '编辑租户用户', 'user', 'update', 'tenant', 1, NOW()),
('user:delete:*', '删除任意用户', 'user', 'delete', '*', 1, NOW()),
('user:delete:tenant', '删除租户用户', 'user', 'delete', 'tenant', 1, NOW());

-- 1.4 角色管理权限
INSERT INTO `sys_permission` (`permission`, `description`, `domain`, `action`, `instance`, `creator`, `create_date`) VALUES
('role:view:*', '查看所有角色', 'role', 'view', '*', 1, NOW()),
('role:view:tenant', '查看租户角色', 'role', 'view', 'tenant', 1, NOW()),
('role:create:platform', '创建平台角色', 'role', 'create', 'platform', 1, NOW()),
('role:create:tenant', '创建租户角色', 'role', 'create', 'tenant', 1, NOW()),
('role:update:*', '编辑任意角色', 'role', 'update', '*', 1, NOW()),
('role:update:tenant', '编辑租户角色', 'role', 'update', 'tenant', 1, NOW()),
('role:delete:*', '删除任意角色', 'role', 'delete', '*', 1, NOW()),
('role:delete:tenant', '删除租户角色', 'role', 'delete', 'tenant', 1, NOW());

-- 1.5 智能体管理权限
INSERT INTO `sys_permission` (`permission`, `description`, `domain`, `action`, `instance`, `creator`, `create_date`) VALUES
('agent:view:*', '查看所有智能体', 'agent', 'view', '*', 1, NOW()),
('agent:view:tenant', '查看租户智能体', 'agent', 'view', 'tenant', 1, NOW()),
('agent:create:*', '创建智能体', 'agent', 'create', '*', 1, NOW()),
('agent:update:*', '编辑智能体', 'agent', 'update', '*', 1, NOW()),
('agent:delete:*', '删除智能体', 'agent', 'delete', '*', 1, NOW()),
('agent:assign:tenant', '分配智能体给租户', 'agent', 'assign', 'tenant', 1, NOW());

-- 1.6 设备管理权限
INSERT INTO `sys_permission` (`permission`, `description`, `domain`, `action`, `instance`, `creator`, `create_date`) VALUES
('device:view:*', '查看所有设备', 'device', 'view', '*', 1, NOW()),
('device:view:tenant', '查看租户设备', 'device', 'view', 'tenant', 1, NOW()),
('device:bind:tenant', '绑定设备', 'device', 'bind', 'tenant', 1, NOW()),
('device:unbind:tenant', '解绑设备', 'device', 'unbind', 'tenant', 1, NOW()),
('device:reset:tenant', '重置设备', 'device', 'reset', 'tenant', 1, NOW()),
('device:disable:tenant', '禁用设备', 'device', 'disable', 'tenant', 1, NOW());

-- 1.7 报表统计权限
INSERT INTO `sys_permission` (`permission`, `description`, `domain`, `action`, `instance`, `creator`, `create_date`) VALUES
('report:view:*', '查看所有报表', 'report', 'view', '*', 1, NOW()),
('report:view:tenant', '查看租户报表', 'report', 'view', 'tenant', 1, NOW()),
('report:export:tenant', '导出租户报表', 'report', 'export', 'tenant', 1, NOW());

-- 1.8 系统配置权限
INSERT INTO `sys_permission` (`permission`, `description`, `domain`, `action`, `instance`, `creator`, `create_date`) VALUES
('system:config:*', '系统配置管理', 'system', 'config', '*', 1, NOW()),
('system:log:view', '查看系统日志', 'system', 'log', 'view', 1, NOW());

-- 2. 初始化角色数据

-- 2.1 平台角色
INSERT INTO `sys_role` (`role_code`, `role_name`, `role_type`, `tenant_id`, `data_scope`, `creator`, `create_date`) VALUES
('PLATFORM_ADMIN', '平台管理员', 1, NULL, 1, 1, NOW()),
('PLATFORM_OPERATOR', '平台运营', 1, NULL, 1, 1, NOW());

-- 2.2 租户角色
INSERT INTO `sys_role` (`role_code`, `role_name`, `role_type`, `tenant_id`, `data_scope`, `creator`, `create_date`) VALUES
('TENANT_ADMIN', '租户管理员', 2, NULL, 2, 1, NOW()),
('TENANT_DEVICE_MANAGER', '设备管理员', 2, NULL, 2, 1, NOW()),
('TENANT_OPERATOR', '租户运营', 2, NULL, 2, 1, NOW()),
('TENANT_USER', '普通用户', 2, NULL, 4, 1, NOW());

-- 3. 初始化角色权限关联

-- 3.1 平台管理员权限（拥有所有权限）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`)
SELECT 
    (SELECT id FROM sys_role WHERE role_code = 'PLATFORM_ADMIN'),
    id,
    1,
    NOW()
FROM sys_permission;

-- 3.2 平台运营权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`)
SELECT 
    (SELECT id FROM sys_role WHERE role_code = 'PLATFORM_OPERATOR'),
    id,
    1,
    NOW()
FROM sys_permission 
WHERE permission IN (
    'organization:view:*',
    'tenant:view:*',
    'user:view:*',
    'agent:view:*',
    'device:view:*',
    'report:view:*'
);

-- 3.3 租户管理员权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`)
SELECT 
    (SELECT id FROM sys_role WHERE role_code = 'TENANT_ADMIN'),
    id,
    1,
    NOW()
FROM sys_permission 
WHERE permission IN (
    'user:view:tenant', 'user:create:tenant', 'user:update:tenant', 'user:delete:tenant',
    'role:view:tenant', 'role:create:tenant', 'role:update:tenant', 'role:delete:tenant',
    'agent:view:tenant',
    'device:view:tenant', 'device:bind:tenant', 'device:unbind:tenant', 'device:reset:tenant', 'device:disable:tenant',
    'report:view:tenant', 'report:export:tenant'
);

-- 3.4 设备管理员权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`)
SELECT 
    (SELECT id FROM sys_role WHERE role_code = 'TENANT_DEVICE_MANAGER'),
    id,
    1,
    NOW()
FROM sys_permission 
WHERE permission IN (
    'agent:view:tenant',
    'device:view:tenant', 'device:bind:tenant', 'device:unbind:tenant', 'device:reset:tenant', 'device:disable:tenant'
);

-- 3.5 租户运营权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`)
SELECT 
    (SELECT id FROM sys_role WHERE role_code = 'TENANT_OPERATOR'),
    id,
    1,
    NOW()
FROM sys_permission 
WHERE permission IN (
    'agent:view:tenant',
    'device:view:tenant',
    'report:view:tenant', 'report:export:tenant'
);

-- 3.6 普通用户权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`)
SELECT 
    (SELECT id FROM sys_role WHERE role_code = 'TENANT_USER'),
    id,
    1,
    NOW()
FROM sys_permission 
WHERE permission IN (
    'agent:view:tenant',
    'device:view:tenant'
);

-- 4. 为现有超级管理员分配平台管理员角色
INSERT INTO `sys_user_role` (`user_id`, `role_id`, `creator`, `create_date`)
SELECT 
    id,
    (SELECT id FROM sys_role WHERE role_code = 'PLATFORM_ADMIN'),
    1,
    NOW()
FROM sys_user 
WHERE super_admin = 1;
