package xiaozhi.modules.security.tenant;

/**
 * 租户上下文管理
 *
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public class TenantContext {

    private static final ThreadLocal<Long> TENANT_ID_HOLDER = new ThreadLocal<>();
    private static final ThreadLocal<Integer> USER_TYPE_HOLDER = new ThreadLocal<>();

    /**
     * 设置当前租户ID
     *
     * @param tenantId 租户ID
     */
    public static void setTenantId(Long tenantId) {
        TENANT_ID_HOLDER.set(tenantId);
    }

    /**
     * 获取当前租户ID
     *
     * @return 租户ID
     */
    public static Long getTenantId() {
        return TENANT_ID_HOLDER.get();
    }

    /**
     * 设置当前用户类型
     *
     * @param userType 用户类型
     */
    public static void setUserType(Integer userType) {
        USER_TYPE_HOLDER.set(userType);
    }

    /**
     * 获取当前用户类型
     *
     * @return 用户类型
     */
    public static Integer getUserType() {
        return USER_TYPE_HOLDER.get();
    }

    /**
     * 检查是否为平台管理员
     *
     * @return 是否为平台管理员
     */
    public static boolean isPlatformAdmin() {
        Integer userType = USER_TYPE_HOLDER.get();
        return userType != null && userType == 1;
    }

    /**
     * 检查是否为租户管理员
     *
     * @return 是否为租户管理员
     */
    public static boolean isTenantAdmin() {
        Integer userType = USER_TYPE_HOLDER.get();
        return userType != null && userType == 2;
    }

    /**
     * 清除当前租户ID和用户类型
     */
    public static void clear() {
        TENANT_ID_HOLDER.remove();
        USER_TYPE_HOLDER.remove();
    }

    /**
     * 检查是否设置了租户ID
     *
     * @return 是否设置了租户ID
     */
    public static boolean hasTenantId() {
        return TENANT_ID_HOLDER.get() != null;
    }
}
