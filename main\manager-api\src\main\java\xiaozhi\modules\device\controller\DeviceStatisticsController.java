package xiaozhi.modules.device.controller;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.device.dto.DeviceStatisticsQueryDTO;
import xiaozhi.modules.device.dto.DeviceStatisticsResponseDTO;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.tenant.TenantContext;

import javax.validation.Valid;

/**
 * 设备统计Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@RestController
@RequestMapping("/device/statistics")
@Tag(name = "设备统计", description = "设备使用统计和分析")
public class DeviceStatisticsController {
    
    private final DeviceService deviceService;
    
    @PostMapping("/query")
    @Operation(
        operationId = "getDeviceStatistics",
        summary = "获取设备使用统计",
        description = "根据查询条件获取设备的使用统计数据，包括连接次数、在线时长、成功率等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "device:statistics:*", description = "查看设备统计")
    public Result<DeviceStatisticsResponseDTO> getDeviceStatistics(
            @Parameter(description = "统计查询条件", required = true)
            @RequestBody @Valid DeviceStatisticsQueryDTO queryDTO) {
        DeviceStatisticsResponseDTO statistics = deviceService.getDeviceStatistics(queryDTO);
        return new Result<DeviceStatisticsResponseDTO>().ok(statistics);
    }
    
    @GetMapping("/active")
    @Operation(
        operationId = "getActiveDevicesRanking",
        summary = "获取活跃设备排行",
        description = "获取指定时间范围内的活跃设备排行榜"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "device:statistics:*", description = "查看活跃设备")
    public Result<List<Map<String, Object>>> getActiveDevicesRanking(
            @Parameter(description = "开始日期", required = true, example = "2025-01-01")
            @RequestParam String startDate,
            @Parameter(description = "结束日期", required = true, example = "2025-01-31")
            @RequestParam String endDate,
            @Parameter(description = "限制数量", example = "10", 
                schema = @Schema(minimum = "1", maximum = "100"))
            @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> ranking = deviceService.getActiveDevicesRanking(startDate, endDate, limit);
        return new Result<List<Map<String, Object>>>().ok(ranking);
    }
    
    @PostMapping("/usage")
    @Operation(
        operationId = "recordDeviceUsage",
        summary = "记录设备使用情况",
        description = "记录设备的使用情况，用于统计分析"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "记录成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "device:usage:*", description = "记录设备使用")
    public Result<Void> recordDeviceUsage(
            @Parameter(description = "设备ID", required = true, example = "device123")
            @RequestParam String deviceId,
            @Parameter(description = "设备MAC地址", required = true, example = "AA:BB:CC:DD:EE:FF")
            @RequestParam String macAddress,
            @Parameter(description = "在线时长（秒）", required = true, example = "3600")
            @RequestParam Long onlineDuration,
            @Parameter(description = "是否成功连接", required = true, example = "true")
            @RequestParam Boolean isSuccess,
            @Parameter(description = "数据传输量（字节）", example = "1048576")
            @RequestParam(defaultValue = "0") Long dataTransferBytes) {
        deviceService.recordDeviceUsage(deviceId, macAddress, onlineDuration, isSuccess, dataTransferBytes);
        return new Result<>();
    }
    
    @GetMapping("/tenant/overview")
    @Operation(
        operationId = "getTenantDeviceOverview",
        summary = "获取租户设备统计概览",
        description = "获取当前租户的设备使用统计概览"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "device:statistics:*", description = "查看租户设备概览")
    public Result<Map<String, Object>> getTenantDeviceOverview() {
        Long tenantId = TenantContext.getTenantId();
        Map<String, Object> overview = deviceService.getTenantDeviceOverview(tenantId);
        return new Result<Map<String, Object>>().ok(overview);
    }
    
    @GetMapping("/tenant/{tenantId}/overview")
    @Operation(
        operationId = "getTenantDeviceOverviewById",
        summary = "获取指定租户设备统计概览",
        description = "获取指定租户的设备使用统计概览（仅平台管理员可用）"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    @RequiresPermission(value = "device:statistics:platform", description = "查看所有租户设备概览")
    public Result<Map<String, Object>> getTenantDeviceOverviewById(
            @Parameter(description = "租户ID", required = true, example = "1")
            @PathVariable Long tenantId) {
        Map<String, Object> overview = deviceService.getTenantDeviceOverview(tenantId);
        return new Result<Map<String, Object>>().ok(overview);
    }
}
