package xiaozhi.modules.device.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备激活记录实体
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@TableName("ai_device_activation")
@Schema(description = "设备激活记录信息")
public class DeviceActivationEntity {

    @TableId(type = IdType.AUTO)
    @Schema(description = "激活记录ID")
    private Long id;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备MAC地址")
    private String macAddress;

    @Schema(description = "激活码")
    private String activationCode;

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "激活状态（0:未激活 1:已激活 2:激活失败）")
    private Integer activationStatus;

    @Schema(description = "激活时间")
    private Date activatedAt;

    @Schema(description = "激活码生成时间")
    private Date codeGeneratedAt;

    @Schema(description = "激活码过期时间")
    private Date codeExpiredAt;

    @Schema(description = "激活IP地址")
    private String activationIp;

    @Schema(description = "设备硬件型号")
    private String board;

    @Schema(description = "固件版本号")
    private String appVersion;

    @Schema(description = "激活失败原因")
    private String failureReason;

    @Schema(description = "重试次数")
    private Integer retryCount;

    @Schema(description = "创建者")
    private Long creator;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新者")
    private Long updater;

    @Schema(description = "更新时间")
    private Date updatedAt;

    @Schema(description = "是否删除（0:未删除 1:已删除）")
    private Integer deleted;
}
