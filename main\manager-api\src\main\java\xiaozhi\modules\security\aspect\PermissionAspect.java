package xiaozhi.modules.security.aspect;

import org.apache.shiro.authz.UnauthorizedException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;
import xiaozhi.common.user.UserDetail;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.service.PermissionService;
import xiaozhi.modules.security.user.SecurityUser;

/**
 * 权限校验AOP切面
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Aspect
@Component
@AllArgsConstructor
public class PermissionAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(PermissionAspect.class);
    
    private final PermissionService permissionService;
    
    @Around("@annotation(requiresPermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, RequiresPermission requiresPermission) throws Throwable {
        UserDetail user = SecurityUser.getUser();
        if (user == null || user.getId() == null) {
            throw new UnauthorizedException("用户未登录");
        }
        
        String permission = requiresPermission.value();
        boolean hasPermission;
        
        if (requiresPermission.tenant()) {
            // 租户权限校验
            hasPermission = permissionService.hasTenantPermission(user.getId(), permission, user.getTenantId());
        } else {
            // 普通权限校验
            hasPermission = permissionService.hasPermission(user.getId(), permission);
        }
        
        if (!hasPermission) {
            String methodName = joinPoint.getSignature().getName();
            String className = joinPoint.getTarget().getClass().getSimpleName();
            logger.warn("权限校验失败 - 用户ID: {}, 权限: {}, 方法: {}.{}", 
                user.getId(), permission, className, methodName);
            throw new UnauthorizedException("无权限访问: " + requiresPermission.description());
        }
        
        return joinPoint.proceed();
    }
}
