<template>
  <div class="organization-management">
    <HeaderBar />
    
    <div class="main-content">
      <div class="content-header">
        <h2>企业管理</h2>
        <div class="header-actions">
          <el-button
            type="primary"
            @click="showCreateDialog = true"

            <i class="el-icon-plus"></i> 新增企业
          </el-button>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="企业名称">
            <el-input v-model="searchForm.organizationName" placeholder="请输入企业名称" clearable />
          </el-form-item>
          <el-form-item label="联系人">
            <el-input v-model="searchForm.contactPerson" placeholder="请输入联系人" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-area">
        <el-table 
          :data="organizationList" 
          v-loading="loading"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="organizationName" label="企业名称" min-width="150" />
          <el-table-column prop="contactPerson" label="联系人" width="120" />
          <el-table-column prop="contactPhone" label="联系电话" width="130" />
          <el-table-column prop="contactEmail" label="联系邮箱" min-width="180" />
          <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="expireDate" label="到期时间" width="120" />
          <el-table-column prop="createDate" label="创建时间" width="120" />
          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleView(scope.row)">查看</el-button>
              <el-button
                size="mini"
                type="primary"
                @click="handleEdit(scope.row)"
                v-permission="PERMISSIONS.ORGANIZATION.UPDATE">
                编辑
              </el-button>
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                v-permission="PERMISSIONS.ORGANIZATION.DELETE">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-area">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="showCreateDialog" 
      width="600px"
      @close="handleDialogClose"
    >
      <el-form :model="organizationForm" :rules="formRules" ref="organizationForm" label-width="100px">
        <el-form-item label="企业名称" prop="organizationName">
          <el-input v-model="organizationForm.organizationName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="organizationForm.contactPerson" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="organizationForm.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="联系邮箱" prop="contactEmail">
          <el-input v-model="organizationForm.contactEmail" placeholder="请输入联系邮箱" />
        </el-form-item>
        <el-form-item label="地址">
          <el-input v-model="organizationForm.address" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="organizationForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入企业描述" 
          />
        </el-form-item>
        <el-form-item label="到期时间" prop="expireDate">
          <el-date-picker
            v-model="organizationForm.expireDate"
            type="date"
            placeholder="选择到期时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="organizationForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="企业详情" :visible.sync="showDetailDialog" width="600px">
      <div class="detail-content" v-if="currentOrganization">
        <div class="detail-item">
          <label>企业名称：</label>
          <span>{{ currentOrganization.organizationName }}</span>
        </div>
        <div class="detail-item">
          <label>联系人：</label>
          <span>{{ currentOrganization.contactPerson }}</span>
        </div>
        <div class="detail-item">
          <label>联系电话：</label>
          <span>{{ currentOrganization.contactPhone }}</span>
        </div>
        <div class="detail-item">
          <label>联系邮箱：</label>
          <span>{{ currentOrganization.contactEmail }}</span>
        </div>
        <div class="detail-item">
          <label>地址：</label>
          <span>{{ currentOrganization.address }}</span>
        </div>
        <div class="detail-item">
          <label>描述：</label>
          <span>{{ currentOrganization.description }}</span>
        </div>
        <div class="detail-item">
          <label>状态：</label>
          <el-tag :type="currentOrganization.status === 1 ? 'success' : 'danger'">
            {{ currentOrganization.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>到期时间：</label>
          <span>{{ currentOrganization.expireDate }}</span>
        </div>
        <div class="detail-item">
          <label>创建时间：</label>
          <span>{{ currentOrganization.createDate }}</span>
        </div>
      </div>
    </el-dialog>

    <VersionFooter />
  </div>
</template>

<script>
import HeaderBar from "@/components/HeaderBar.vue";
import VersionFooter from "@/components/VersionFooter.vue";
import organizationApi from "@/apis/module/organization";

export default {
  name: "OrganizationManagement",
  components: {
    HeaderBar,
    VersionFooter
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        organizationName: '',
        contactPerson: '',
        status: null
      },
      // 数据列表
      organizationList: [],
      loading: false,
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 对话框
      showCreateDialog: false,
      showDetailDialog: false,
      dialogTitle: '新增企业',
      isEdit: false,
      submitting: false,
      // 表单数据
      organizationForm: {
        id: null,
        organizationName: '',
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
        address: '',
        description: '',
        expireDate: '',
        status: 1
      },
      // 当前查看的企业
      currentOrganization: null,
      // 表单验证规则
      formRules: {
        organizationName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' }
        ],
        contactPerson: [
          { required: true, message: '请输入联系人', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        contactEmail: [
          { required: true, message: '请输入联系邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        expireDate: [
          { required: true, message: '请选择到期时间', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    // 权限控制方法已通过混入提供，无需重复定义
  },
  created() {
    this.fetchOrganizations();
  },
  methods: {
    // 获取企业列表
    fetchOrganizations() {
      this.loading = true;
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        ...this.searchForm
      };
      
      organizationApi.getOrganizationPage(params, (res) => {
        this.loading = false;
        if (res.code === 0) {
          this.organizationList = res.data.list || [];
          this.total = res.data.total || 0;
        } else {
          this.$message.error(res.msg || '获取企业列表失败');
        }
      });
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.fetchOrganizations();
    },
    
    // 重置搜索
    handleReset() {
      this.searchForm = {
        organizationName: '',
        contactPerson: '',
        status: null
      };
      this.currentPage = 1;
      this.fetchOrganizations();
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.fetchOrganizations();
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchOrganizations();
    },
    
    // 查看详情
    handleView(row) {
      this.currentOrganization = row;
      this.showDetailDialog = true;
    },
    
    // 编辑
    handleEdit(row) {
      this.isEdit = true;
      this.dialogTitle = '编辑企业';
      this.organizationForm = { ...row };
      this.showCreateDialog = true;
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm('确定要删除该企业吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        organizationApi.deleteOrganization(row.id, (res) => {
          if (res.code === 0) {
            this.$message.success('删除成功');
            this.fetchOrganizations();
          } else {
            this.$message.error(res.msg || '删除失败');
          }
        });
      });
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.organizationForm.validate((valid) => {
        if (valid) {
          this.submitting = true;
          const apiMethod = this.isEdit ? 'updateOrganization' : 'createOrganization';
          
          organizationApi[apiMethod](this.organizationForm, (res) => {
            this.submitting = false;
            if (res.code === 0) {
              this.$message.success(this.isEdit ? '更新成功' : '创建成功');
              this.showCreateDialog = false;
              this.fetchOrganizations();
            } else {
              this.$message.error(res.msg || (this.isEdit ? '更新失败' : '创建失败'));
            }
          });
        }
      });
    },
    
    // 对话框关闭
    handleDialogClose() {
      this.isEdit = false;
      this.dialogTitle = '新增企业';
      this.organizationForm = {
        id: null,
        organizationName: '',
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
        address: '',
        description: '',
        expireDate: '',
        status: 1
      };
      this.$refs.organizationForm && this.$refs.organizationForm.resetFields();
    }
  }
};
</script>

<style scoped>
.organization-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.content-header h2 {
  margin: 0;
  color: #333;
}

.search-area {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-area {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-area {
  margin-top: 20px;
  text-align: right;
}

.detail-content {
  padding: 10px 0;
}

.detail-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
}

.detail-item label {
  width: 100px;
  font-weight: bold;
  color: #666;
  flex-shrink: 0;
}

.detail-item span {
  flex: 1;
  word-break: break-all;
}
</style>
