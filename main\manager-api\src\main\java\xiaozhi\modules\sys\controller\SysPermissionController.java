package xiaozhi.modules.sys.controller;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.security.utils.PermissionUtils;
import xiaozhi.modules.sys.entity.SysPermissionEntity;
import xiaozhi.modules.sys.service.SysPermissionService;

/**
 * 权限管理Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/sys/permission")
@Tag(name = "权限管理", description = "系统权限管理相关接口，包括权限查询、校验等功能")
@AllArgsConstructor
public class SysPermissionController {
    
    private final SysPermissionService sysPermissionService;
    private final PermissionUtils permissionUtils;
    
    @GetMapping("/user/permissions")
    @Operation(
        summary = "获取当前用户权限列表",
        description = "获取当前登录用户拥有的所有权限字符串列表，用于前端权限控制"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功",
            content = @Content(schema = @Schema(implementation = Result.class))),
        @ApiResponse(responseCode = "401", description = "用户未登录"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Set<String>> getUserPermissions() {
        Long userId = SecurityUser.getUserId();
        Set<String> permissions = sysPermissionService.getUserPermissions(userId);
        return new Result<Set<String>>().ok(permissions);
    }
    
    @GetMapping("/role/{roleId}")
    @Operation(
        summary = "获取角色权限列表",
        description = "根据角色ID获取该角色拥有的所有权限详细信息"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "角色不存在")
    })
    @RequiresPermission(value = "role:view:*", description = "查看角色权限")
    public Result<List<SysPermissionEntity>> getRolePermissions(
            @Parameter(description = "角色ID", required = true, example = "1")
            @PathVariable Long roleId) {
        List<SysPermissionEntity> permissions = sysPermissionService.getPermissionsByRoleId(roleId);
        return new Result<List<SysPermissionEntity>>().ok(permissions);
    }
    
    @GetMapping("/domain/{domain}/action/{action}")
    @Operation(
        summary = "根据权限域和操作获取权限列表",
        description = "根据权限域（如user、device）和操作类型（如view、create）查询相关权限"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "system:config:*", description = "系统配置权限")
    public Result<List<SysPermissionEntity>> getPermissionsByDomainAndAction(
            @Parameter(description = "权限域", required = true, example = "user")
            @PathVariable String domain,
            @Parameter(description = "操作类型", required = true, example = "view")
            @PathVariable String action) {
        List<SysPermissionEntity> permissions = sysPermissionService.getPermissionsByDomainAndAction(domain, action);
        return new Result<List<SysPermissionEntity>>().ok(permissions);
    }
    
    @GetMapping("/check/{permission}")
    @Operation(
        summary = "检查当前用户是否有指定权限",
        description = "检查当前登录用户是否拥有指定的权限字符串，返回true/false"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "检查完成"),
        @ApiResponse(responseCode = "401", description = "用户未登录")
    })
    public Result<Boolean> checkPermission(
            @Parameter(description = "权限字符串", required = true, example = "user:view:*")
            @PathVariable String permission) {
        boolean hasPermission = permissionUtils.hasPermission(permission);
        return new Result<Boolean>().ok(hasPermission);
    }
    
    @GetMapping("/check/tenant/{permission}")
    @Operation(
        summary = "检查当前用户是否有租户权限",
        description = "检查当前登录用户是否拥有指定的租户级权限，会自动替换权限字符串中的tenant占位符"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "检查完成"),
        @ApiResponse(responseCode = "401", description = "用户未登录")
    })
    public Result<Boolean> checkTenantPermission(
            @Parameter(description = "租户权限字符串", required = true, example = "device:bind:tenant")
            @PathVariable String permission) {
        boolean hasPermission = permissionUtils.hasTenantPermission(permission);
        return new Result<Boolean>().ok(hasPermission);
    }
    
    @GetMapping("/user/info")
    @Operation(
        summary = "获取当前用户权限信息",
        description = "获取当前登录用户的详细权限信息，包括用户ID、租户ID、用户类型、管理员状态和权限列表"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "用户未登录")
    })
    public Result<Object> getUserInfo() {
        return new Result<>().ok(Map.of(
            "userId", Optional.ofNullable(SecurityUser.getUserId()).orElse(0L),
            "tenantId", Optional.ofNullable(SecurityUser.getTenantId()).orElse(0L),
            "userType", Optional.ofNullable(SecurityUser.getUserType()).orElse(2),
            "isPlatformAdmin", permissionUtils.isPlatformAdmin(),
            "isTenantAdmin", permissionUtils.isTenantAdmin(),
            "permissions", permissionUtils.getUserPermissions()
        ));
    }
}
