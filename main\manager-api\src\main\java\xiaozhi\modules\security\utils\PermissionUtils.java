package xiaozhi.modules.security.utils;

import java.util.Set;

import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;
import xiaozhi.common.user.UserDetail;
import xiaozhi.modules.security.service.PermissionService;
import xiaozhi.modules.security.tenant.TenantContext;
import xiaozhi.modules.security.user.SecurityUser;

/**
 * 权限工具类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Component
@AllArgsConstructor
public class PermissionUtils {
    
    private final PermissionService permissionService;
    
    /**
     * 检查当前用户是否有指定权限
     * 
     * @param permission 权限字符串
     * @return 是否有权限
     */
    public boolean hasPermission(String permission) {
        UserDetail user = SecurityUser.getUser();
        if (user == null || user.getId() == null) {
            return false;
        }
        return permissionService.hasPermission(user.getId(), permission);
    }
    
    /**
     * 检查当前用户是否有租户权限
     * 
     * @param permission 权限字符串
     * @return 是否有权限
     */
    public boolean hasTenantPermission(String permission) {
        UserDetail user = SecurityUser.getUser();
        if (user == null || user.getId() == null || user.getTenantId() == null) {
            return false;
        }
        return permissionService.hasTenantPermission(user.getId(), permission, user.getTenantId());
    }
    
    /**
     * 检查当前用户是否有指定租户的权限
     * 
     * @param permission 权限字符串
     * @param tenantId 租户ID
     * @return 是否有权限
     */
    public boolean hasTenantPermission(String permission, Long tenantId) {
        UserDetail user = SecurityUser.getUser();
        if (user == null || user.getId() == null) {
            return false;
        }
        return permissionService.hasTenantPermission(user.getId(), permission, tenantId);
    }
    
    /**
     * 检查当前用户是否有任意一个权限
     * 
     * @param permissions 权限字符串数组
     * @return 是否有权限
     */
    public boolean hasAnyPermission(String... permissions) {
        UserDetail user = SecurityUser.getUser();
        if (user == null || user.getId() == null) {
            return false;
        }
        return permissionService.hasAnyPermission(user.getId(), permissions);
    }
    
    /**
     * 检查当前用户是否有所有权限
     * 
     * @param permissions 权限字符串数组
     * @return 是否有权限
     */
    public boolean hasAllPermissions(String... permissions) {
        UserDetail user = SecurityUser.getUser();
        if (user == null || user.getId() == null) {
            return false;
        }
        return permissionService.hasAllPermissions(user.getId(), permissions);
    }
    
    /**
     * 获取当前用户权限列表
     * 
     * @return 权限字符串集合
     */
    public Set<String> getUserPermissions() {
        UserDetail user = SecurityUser.getUser();
        if (user == null || user.getId() == null) {
            return Set.of();
        }
        return permissionService.getUserPermissions(user.getId());
    }
    
    /**
     * 检查当前用户是否为平台管理员
     * 
     * @return 是否为平台管理员
     */
    public boolean isPlatformAdmin() {
        return hasPermission("*:*:*") || hasPermission("organization:view:*");
    }
    
    /**
     * 检查当前用户是否为租户管理员
     * 
     * @return 是否为租户管理员
     */
    public boolean isTenantAdmin() {
        return hasTenantPermission("user:create:tenant");
    }
    
    /**
     * 获取当前租户ID
     * 
     * @return 租户ID
     */
    public Long getCurrentTenantId() {
        // 优先从租户上下文获取
        Long tenantId = TenantContext.getTenantId();
        if (tenantId != null) {
            return tenantId;
        }
        
        // 从用户信息获取
        UserDetail user = SecurityUser.getUser();
        if (user != null) {
            return user.getTenantId();
        }
        
        return null;
    }
}
