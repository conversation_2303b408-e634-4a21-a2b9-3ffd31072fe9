<template>
  <div class="test-page">
    <!-- 测试HeaderBar组件 -->
    <HeaderBar />
    
    <el-main style="padding: 20px;">
      <el-card>
        <div slot="header">
          <span>权限系统测试页面</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <h3>系统状态</h3>
            <el-alert 
              title="✅ Vue.js 加载成功" 
              type="success" 
              :closable="false"
              style="margin-bottom: 10px;">
            </el-alert>
            <el-alert 
              title="✅ Element UI 加载成功" 
              type="success" 
              :closable="false"
              style="margin-bottom: 10px;">
            </el-alert>
            <el-alert 
              title="✅ HeaderBar 组件渲染成功" 
              type="success" 
              :closable="false"
              style="margin-bottom: 10px;">
            </el-alert>
            <el-alert 
              title="✅ 权限指令临时禁用（显示所有元素）" 
              type="warning" 
              :closable="false">
            </el-alert>
          </el-col>
          
          <el-col :span="12">
            <h3>权限测试</h3>
            <div style="margin-bottom: 15px;">
              <el-button type="primary" v-permission="'user:create:tenant'">
                创建用户（权限测试）
              </el-button>
              <el-button type="success" v-permission="'organization:view:*'">
                查看企业（权限测试）
              </el-button>
            </div>
            
            <div style="margin-bottom: 15px;">
              <el-button type="warning" v-permission-disabled="'admin:delete:*'">
                删除操作（禁用测试）
              </el-button>
            </div>
            
            <div>
              <p><strong>权限常量测试:</strong></p>
              <p>USER.VIEW_ALL: {{ PERMISSIONS.USER.VIEW_ALL }}</p>
              <p>ORGANIZATION.VIEW_ALL: {{ PERMISSIONS.ORGANIZATION.VIEW_ALL }}</p>
            </div>
          </el-col>
        </el-row>
        
        <el-divider></el-divider>
        
        <el-row>
          <el-col :span="24">
            <h3>导航测试</h3>
            <el-button-group>
              <el-button @click="$router.push('/home')">首页</el-button>
              <el-button @click="$router.push('/user-management')">用户管理</el-button>
              <el-button @click="$router.push('/organization-management')">企业管理</el-button>
              <el-button @click="$router.push('/tenant-management')">租户管理</el-button>
              <el-button @click="$router.push('/permission-management')">权限管理</el-button>
              <el-button @click="$router.push('/permission-debug')">权限调试</el-button>
            </el-button-group>
          </el-col>
        </el-row>
        
        <el-divider></el-divider>
        
        <el-row>
          <el-col :span="24">
            <h3>下一步操作</h3>
            <ol>
              <li>确认HeaderBar导航菜单显示正常</li>
              <li>测试各个导航链接是否可以正常跳转</li>
              <li>验证权限指令是否按预期工作（当前应该显示所有元素）</li>
              <li>逐步启用完整的权限系统</li>
            </ol>
          </el-col>
        </el-row>
      </el-card>
    </el-main>
  </div>
</template>

<script>
import HeaderBar from '@/components/HeaderBar.vue';

export default {
  name: 'TestPage',
  components: {
    HeaderBar
  },
  data() {
    return {
      // 测试数据
    }
  },
  methods: {
    // 测试方法
  }
}
</script>

<style scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

h3 {
  color: #333;
  margin-bottom: 15px;
}
</style>
