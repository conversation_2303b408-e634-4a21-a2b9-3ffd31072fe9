import { hasPermission, hasAnyPermission, hasAllPermissions, PERMISSIONS, PermissionChecker } from '@/utils/permission'

/**
 * 权限管理混入
 * 为所有组件提供权限检查方法
 */
export default {
  methods: {
    // 基础权限检查方法
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    
    // 便捷权限检查方法
    ...PermissionChecker,
    
    /**
     * 检查是否为平台管理员
     */
    isPlatformAdmin() {
      return this.$store.getters.userType === 1
    },
    
    /**
     * 检查是否为租户管理员
     */
    isTenantAdmin() {
      return this.$store.getters.userType === 2
    },
    
    /**
     * 检查是否为普通用户
     */
    isNormalUser() {
      return this.$store.getters.userType === 3
    },
    
    /**
     * 获取当前用户类型
     */
    getCurrentUserType() {
      return this.$store.getters.userType
    },
    
    /**
     * 获取当前用户权限列表
     */
    getCurrentPermissions() {
      return this.$store.getters.permissions
    },
    
    /**
     * 获取当前用户角色列表
     */
    getCurrentRoles() {
      return this.$store.getters.roles
    },
    
    /**
     * 权限错误处理
     */
    handlePermissionError(message = '您没有权限执行此操作') {
      this.$message.error(message)
    },
    
    /**
     * 检查并执行操作
     * @param {String|Array} permission 权限字符串或数组
     * @param {Function} callback 有权限时执行的回调
     * @param {Function} errorCallback 无权限时执行的回调
     */
    checkAndExecute(permission, callback, errorCallback) {
      let hasAuth = false
      
      if (Array.isArray(permission)) {
        hasAuth = hasAnyPermission(permission)
      } else {
        hasAuth = hasPermission(permission)
      }
      
      if (hasAuth) {
        if (typeof callback === 'function') {
          callback()
        }
      } else {
        if (typeof errorCallback === 'function') {
          errorCallback()
        } else {
          this.handlePermissionError()
        }
      }
    },
    
    /**
     * 权限确认对话框
     * @param {String|Array} permission 权限字符串或数组
     * @param {String} message 确认消息
     * @param {Function} callback 确认后执行的回调
     */
    confirmWithPermission(permission, message, callback) {
      this.checkAndExecute(permission, () => {
        this.$confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (typeof callback === 'function') {
            callback()
          }
        }).catch(() => {
          // 用户取消操作
        })
      })
    }
  },
  
  computed: {
    // 权限常量
    PERMISSIONS: () => PERMISSIONS,
    
    // 当前用户信息
    currentUser() {
      return this.$store.getters.getUserInfo
    },
    
    // 当前用户类型
    userType() {
      return this.$store.getters.userType
    },
    
    // 当前用户权限
    userPermissions() {
      return this.$store.getters.permissions
    },
    
    // 当前用户角色
    userRoles() {
      return this.$store.getters.roles
    },
    
    // 用户类型文本
    userTypeText() {
      const typeMap = {
        1: '平台管理员',
        2: '租户管理员',
        3: '普通用户'
      }
      return typeMap[this.userType] || '未知'
    }
  }
}
