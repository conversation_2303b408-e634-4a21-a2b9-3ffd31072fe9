# Swagger注解使用指南

## 1. 概述

本项目使用OpenAPI 3.0（Swagger 3）规范为所有Controller接口添加详细的API文档注解，方便开发调试和接口测试。

## 2. 注解规范

### 2.1 Controller类级别注解

```java
@RestController
@RequestMapping("/api/example")
@Tag(name = "模块名称", description = "模块功能的详细描述")
public class ExampleController {
    // ...
}
```

**必需注解**：
- `@Tag(name, description)` - 定义API分组和描述

### 2.2 方法级别注解

```java
@GetMapping("/list")
@Operation(
    summary = "简短的功能描述",
    description = "详细的功能说明，包括业务逻辑和使用场景"
)
@ApiResponses({
    @ApiResponse(responseCode = "200", description = "成功描述"),
    @ApiResponse(responseCode = "403", description = "权限不足"),
    @ApiResponse(responseCode = "500", description = "服务器错误")
})
public Result<List<Entity>> list() {
    // ...
}
```

**必需注解**：
- `@Operation(summary, description)` - 接口功能描述
- `@ApiResponses` - 响应状态码说明

### 2.3 参数注解

```java
@GetMapping("/{id}")
public Result<Entity> getById(
    @Parameter(description = "实体ID", required = true, example = "1")
    @PathVariable Long id) {
    // ...
}

@GetMapping("/search")
public Result<List<Entity>> search(
    @Parameter(description = "搜索关键词", example = "测试")
    @RequestParam(required = false) String keyword,
    
    @Parameter(description = "页码", example = "1", 
        schema = @Schema(minimum = "1"))
    @RequestParam(defaultValue = "1") Integer page) {
    // ...
}
```

**参数注解**：
- `@Parameter(description, required, example, schema)` - 参数说明

### 2.4 请求体注解

```java
@PostMapping("/create")
public Result<Void> create(
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "创建实体的请求数据",
        required = true,
        content = @Content(schema = @Schema(implementation = CreateRequest.class))
    )
    @RequestBody CreateRequest request) {
    // ...
}
```

## 3. 项目中的实际应用

### 3.1 权限管理Controller

```java
@RestController
@RequestMapping("/sys/permission")
@Tag(name = "权限管理", description = "系统权限管理相关接口，包括权限查询、校验等功能")
public class SysPermissionController {
    
    @GetMapping("/user/permissions")
    @Operation(
        summary = "获取当前用户权限列表",
        description = "获取当前登录用户拥有的所有权限字符串列表，用于前端权限控制"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "用户未登录"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Set<String>> getUserPermissions() {
        // ...
    }
}
```

### 3.2 租户管理Controller

```java
@RestController
@RequestMapping("/sys/tenant")
@Tag(name = "租户管理", description = "多租户系统的租户管理相关接口")
public class SysTenantController {
    
    @GetMapping("/{id}")
    @Operation(
        summary = "获取租户详情",
        description = "根据租户ID获取租户的详细信息，包括租户配置、状态等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    public Result<SysTenantEntity> info(
        @Parameter(description = "租户ID", required = true, example = "1")
        @PathVariable Long id) {
        // ...
    }
}
```

### 3.3 权限测试Controller

```java
@RestController
@RequestMapping("/test/permission")
@Tag(name = "权限测试", description = "权限框架功能测试接口，用于验证不同级别的权限控制")
public class PermissionTestController {
    
    @GetMapping("/platform/admin")
    @Operation(
        summary = "平台管理员权限测试",
        description = "测试平台管理员权限，需要organization:view:*权限才能访问"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "权限验证通过"),
        @ApiResponse(responseCode = "403", description = "权限不足，需要平台管理员权限"),
        @ApiResponse(responseCode = "401", description = "用户未登录")
    })
    @RequiresPermission(value = "organization:view:*", description = "需要平台管理员权限")
    public Result<String> platformAdminTest() {
        // ...
    }
}
```

## 4. 响应状态码规范

### 4.1 通用状态码

| 状态码 | 描述 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 参数验证失败 |
| 401 | 未认证 | 用户未登录 |
| 403 | 权限不足 | 用户已登录但无权限 |
| 404 | 资源不存在 | 查询的资源不存在 |
| 500 | 服务器错误 | 系统内部错误 |

### 4.2 权限相关状态码

```java
@ApiResponses({
    @ApiResponse(responseCode = "200", description = "操作成功"),
    @ApiResponse(responseCode = "401", description = "用户未登录"),
    @ApiResponse(responseCode = "403", description = "权限不足，需要XXX权限"),
    @ApiResponse(responseCode = "404", description = "资源不存在")
})
```

## 5. 最佳实践

### 5.1 描述规范

1. **summary**：简短明了，动词开头，如"获取用户列表"
2. **description**：详细说明功能、业务逻辑、使用场景
3. **parameter description**：说明参数含义、格式要求、示例值

### 5.2 示例值规范

```java
@Parameter(description = "用户ID", required = true, example = "1")
@Parameter(description = "用户名", example = "admin")
@Parameter(description = "页码", example = "1", schema = @Schema(minimum = "1"))
@Parameter(description = "页大小", example = "10", schema = @Schema(minimum = "1", maximum = "100"))
```

### 5.3 权限注解结合

```java
@GetMapping("/admin/users")
@Operation(
    summary = "管理员查看用户列表",
    description = "管理员权限接口，可以查看所有用户信息"
)
@ApiResponses({
    @ApiResponse(responseCode = "200", description = "查询成功"),
    @ApiResponse(responseCode = "403", description = "权限不足，需要管理员权限")
})
@RequiresPermission(value = "user:view:*", description = "查看用户权限")
public Result<List<User>> getUsers() {
    // ...
}
```

## 6. Swagger UI访问

### 6.1 访问地址

```
开发环境: http://localhost:8002/xiaozhi/doc.html
```

### 6.2 API分组

- **权限管理** - 权限查询、校验相关接口
- **租户管理** - 租户CRUD操作接口
- **权限测试** - 权限框架功能测试接口
- **Service方法演示** - BaseService方法使用演示

## 7. 注意事项

### 7.1 必须添加的注解

每个Controller都必须添加：
- `@Tag` - 类级别分组
- `@Operation` - 方法级别描述
- `@ApiResponses` - 响应状态码
- `@Parameter` - 参数描述（如有参数）

### 7.2 权限相关接口

权限相关接口必须说明：
- 需要的权限级别
- 403状态码的具体原因
- 权限不足时的提示信息

### 7.3 后续开发规范

**重要提醒**：在后续处理与Controller层相关的任务时，都必须按照本指南添加完整的Swagger注解，确保API文档的完整性和一致性。

## 8. 总结

通过规范的Swagger注解，我们实现了：

- ✅ 完整的API文档
- ✅ 清晰的接口分组
- ✅ 详细的参数说明
- ✅ 标准的响应码规范
- ✅ 权限要求说明
- ✅ 便捷的接口测试

这将大大提高开发效率和接口的可维护性。
