package xiaozhi.modules.security.service;

import java.util.Set;

/**
 * 权限服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface PermissionService {
    
    /**
     * 获取用户权限列表
     * 
     * @param userId 用户ID
     * @return 权限字符串集合
     */
    Set<String> getUserPermissions(Long userId);
    
    /**
     * 检查用户是否有指定权限
     * 
     * @param userId 用户ID
     * @param permission 权限字符串
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permission);
    
    /**
     * 检查用户是否有租户权限
     * 
     * @param userId 用户ID
     * @param permission 权限字符串
     * @param tenantId 租户ID
     * @return 是否有权限
     */
    boolean hasTenantPermission(Long userId, String permission, Long tenantId);
    
    /**
     * 检查用户是否有任意一个权限
     * 
     * @param userId 用户ID
     * @param permissions 权限字符串数组
     * @return 是否有权限
     */
    boolean hasAnyPermission(Long userId, String... permissions);
    
    /**
     * 检查用户是否有所有权限
     * 
     * @param userId 用户ID
     * @param permissions 权限字符串数组
     * @return 是否有权限
     */
    boolean hasAllPermissions(Long userId, String... permissions);
}
