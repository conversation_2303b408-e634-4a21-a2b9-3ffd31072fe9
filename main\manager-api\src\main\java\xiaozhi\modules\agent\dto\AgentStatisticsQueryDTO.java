package xiaozhi.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智能体统计查询DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "智能体统计查询")
public class AgentStatisticsQueryDTO {
    
    @Schema(description = "智能体ID", example = "agent123")
    private String agentId;
    
    @Schema(description = "用户ID", example = "1")
    private Long userId;
    
    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01-01")
    private String startDate;
    
    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01-31")
    private String endDate;
    
    @Schema(description = "统计类型", example = "daily", allowableValues = {"daily", "weekly", "monthly"})
    private String statisticsType = "daily";
}
