package xiaozhi.modules.security.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限校验注解
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresPermission {
    
    /**
     * 权限字符串
     */
    String value();
    
    /**
     * 是否为租户权限
     * 如果为true，会自动替换权限字符串中的tenant占位符为当前用户的租户ID
     */
    boolean tenant() default false;
    
    /**
     * 权限描述
     */
    String description() default "";
}
