<template>
  <div class="tenant-management">
    <HeaderBar />
    
    <div class="main-content">
      <div class="content-header">
        <h2>租户管理</h2>
        <div class="header-actions">
          <el-button
            type="primary"
            @click="showCreateDialog = true"
            v-permission="PERMISSIONS.TENANT.CREATE">
            <i class="el-icon-plus"></i> 新增租户
          </el-button>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="租户名称">
            <el-input v-model="searchForm.tenantName" placeholder="请输入租户名称" clearable />
          </el-form-item>
          <el-form-item label="企业">
            <el-select v-model="searchForm.organizationId" placeholder="请选择企业" clearable>
              <el-option 
                v-for="org in organizationList" 
                :key="org.id" 
                :label="org.organizationName" 
                :value="org.id" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-area">
        <el-table 
          :data="tenantList" 
          v-loading="loading"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="tenantName" label="租户名称" min-width="150" />
          <el-table-column prop="organizationName" label="所属企业" min-width="150" />
          <el-table-column prop="contactPerson" label="联系人" width="120" />
          <el-table-column prop="contactPhone" label="联系电话" width="130" />
          <el-table-column prop="maxUsers" label="用户配额" width="100" />
          <el-table-column prop="maxAgents" label="智能体配额" width="120" />
          <el-table-column prop="maxDevices" label="设备配额" width="100" />
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="expireDate" label="到期时间" width="120" />
          <el-table-column prop="createDate" label="创建时间" width="120" />
          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleView(scope.row)">查看</el-button>
              <el-button
                size="mini"
                type="primary"
                @click="handleEdit(scope.row)"
                v-permission="PERMISSIONS.TENANT.UPDATE">
                编辑
              </el-button>
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                v-permission="PERMISSIONS.TENANT.DELETE">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-area">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="showCreateDialog" 
      width="700px"
      @close="handleDialogClose"
    >
      <el-form :model="tenantForm" :rules="formRules" ref="tenantForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="租户名称" prop="tenantName">
              <el-input v-model="tenantForm.tenantName" placeholder="请输入租户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属企业" prop="organizationId">
              <el-select v-model="tenantForm.organizationId" placeholder="请选择企业" style="width: 100%">
                <el-option 
                  v-for="org in organizationList" 
                  :key="org.id" 
                  :label="org.organizationName" 
                  :value="org.id" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="tenantForm.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="tenantForm.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="联系邮箱" prop="contactEmail">
          <el-input v-model="tenantForm.contactEmail" placeholder="请输入联系邮箱" />
        </el-form-item>

        <el-form-item label="描述">
          <el-input 
            v-model="tenantForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入租户描述" 
          />
        </el-form-item>

        <!-- 配额设置 -->
        <div class="quota-section">
          <h4>配额设置</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="用户配额" prop="maxUsers">
                <el-input-number 
                  v-model="tenantForm.maxUsers" 
                  :min="1" 
                  :max="10000"
                  style="width: 100%" 
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="智能体配额" prop="maxAgents">
                <el-input-number 
                  v-model="tenantForm.maxAgents" 
                  :min="1" 
                  :max="1000"
                  style="width: 100%" 
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备配额" prop="maxDevices">
                <el-input-number 
                  v-model="tenantForm.maxDevices" 
                  :min="1" 
                  :max="10000"
                  style="width: 100%" 
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="到期时间" prop="expireDate">
              <el-date-picker
                v-model="tenantForm.expireDate"
                type="date"
                placeholder="选择到期时间"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="tenantForm.status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="租户详情" :visible.sync="showDetailDialog" width="700px">
      <div class="detail-content" v-if="currentTenant">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>租户名称：</label>
              <span>{{ currentTenant.tenantName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>所属企业：</label>
              <span>{{ currentTenant.organizationName }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>联系人：</label>
              <span>{{ currentTenant.contactPerson }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>联系电话：</label>
              <span>{{ currentTenant.contactPhone }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="detail-item">
          <label>联系邮箱：</label>
          <span>{{ currentTenant.contactEmail }}</span>
        </div>

        <div class="detail-item">
          <label>描述：</label>
          <span>{{ currentTenant.description }}</span>
        </div>

        <div class="quota-info">
          <h4>配额信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="quota-item">
                <span class="quota-label">用户配额：</span>
                <span class="quota-value">{{ currentTenant.maxUsers }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="quota-item">
                <span class="quota-label">智能体配额：</span>
                <span class="quota-value">{{ currentTenant.maxAgents }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="quota-item">
                <span class="quota-label">设备配额：</span>
                <span class="quota-value">{{ currentTenant.maxDevices }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="currentTenant.status === 1 ? 'success' : 'danger'">
                {{ currentTenant.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>到期时间：</label>
              <span>{{ currentTenant.expireDate }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="detail-item">
          <label>创建时间：</label>
          <span>{{ currentTenant.createDate }}</span>
        </div>
      </div>
    </el-dialog>

    <VersionFooter />
  </div>
</template>

<script>
import HeaderBar from "@/components/HeaderBar.vue";
import VersionFooter from "@/components/VersionFooter.vue";
import tenantApi from "@/apis/module/tenant";
import organizationApi from "@/apis/module/organization";

export default {
  name: "TenantManagement",
  components: {
    HeaderBar,
    VersionFooter
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        tenantName: '',
        organizationId: null,
        status: null
      },
      // 数据列表
      tenantList: [],
      organizationList: [],
      loading: false,
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 对话框
      showCreateDialog: false,
      showDetailDialog: false,
      dialogTitle: '新增租户',
      isEdit: false,
      submitting: false,
      // 表单数据
      tenantForm: {
        id: null,
        tenantName: '',
        organizationId: null,
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
        description: '',
        maxUsers: 100,
        maxAgents: 10,
        maxDevices: 100,
        expireDate: '',
        status: 1
      },
      // 当前查看的租户
      currentTenant: null,
      // 表单验证规则
      formRules: {
        tenantName: [
          { required: true, message: '请输入租户名称', trigger: 'blur' }
        ],
        organizationId: [
          { required: true, message: '请选择所属企业', trigger: 'change' }
        ],
        contactPerson: [
          { required: true, message: '请输入联系人', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        contactEmail: [
          { required: true, message: '请输入联系邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        maxUsers: [
          { required: true, message: '请设置用户配额', trigger: 'blur' }
        ],
        maxAgents: [
          { required: true, message: '请设置智能体配额', trigger: 'blur' }
        ],
        maxDevices: [
          { required: true, message: '请设置设备配额', trigger: 'blur' }
        ],
        expireDate: [
          { required: true, message: '请选择到期时间', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    // 权限控制方法已通过混入提供，无需重复定义
  },
  created() {
    this.fetchOrganizations();
    this.fetchTenants();
  },
  methods: {
    // 获取企业列表
    fetchOrganizations() {
      organizationApi.getOrganizationPage({ page: 1, limit: 1000 }, (res) => {
        if (res.code === 0) {
          this.organizationList = res.data.list || [];
        }
      });
    },

    // 获取租户列表
    fetchTenants() {
      this.loading = true;
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        ...this.searchForm
      };
      
      tenantApi.getTenantPage(params, (res) => {
        this.loading = false;
        if (res.code === 0) {
          this.tenantList = res.data.list || [];
          this.total = res.data.total || 0;
        } else {
          this.$message.error(res.msg || '获取租户列表失败');
        }
      });
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.fetchTenants();
    },
    
    // 重置搜索
    handleReset() {
      this.searchForm = {
        tenantName: '',
        organizationId: null,
        status: null
      };
      this.currentPage = 1;
      this.fetchTenants();
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.fetchTenants();
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchTenants();
    },
    
    // 查看详情
    handleView(row) {
      this.currentTenant = row;
      this.showDetailDialog = true;
    },
    
    // 编辑
    handleEdit(row) {
      this.isEdit = true;
      this.dialogTitle = '编辑租户';
      this.tenantForm = { ...row };
      this.showCreateDialog = true;
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm('确定要删除该租户吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tenantApi.deleteTenant(row.id, (res) => {
          if (res.code === 0) {
            this.$message.success('删除成功');
            this.fetchTenants();
          } else {
            this.$message.error(res.msg || '删除失败');
          }
        });
      });
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.tenantForm.validate((valid) => {
        if (valid) {
          this.submitting = true;
          const apiMethod = this.isEdit ? 'updateTenant' : 'createTenant';
          
          tenantApi[apiMethod](this.tenantForm, (res) => {
            this.submitting = false;
            if (res.code === 0) {
              this.$message.success(this.isEdit ? '更新成功' : '创建成功');
              this.showCreateDialog = false;
              this.fetchTenants();
            } else {
              this.$message.error(res.msg || (this.isEdit ? '更新失败' : '创建失败'));
            }
          });
        }
      });
    },
    
    // 对话框关闭
    handleDialogClose() {
      this.isEdit = false;
      this.dialogTitle = '新增租户';
      this.tenantForm = {
        id: null,
        tenantName: '',
        organizationId: null,
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
        description: '',
        maxUsers: 100,
        maxAgents: 10,
        maxDevices: 100,
        expireDate: '',
        status: 1
      };
      this.$refs.tenantForm && this.$refs.tenantForm.resetFields();
    }
  }
};
</script>

<style scoped>
.tenant-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.content-header h2 {
  margin: 0;
  color: #333;
}

.search-area {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-area {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-area {
  margin-top: 20px;
  text-align: right;
}

.quota-section {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.quota-section h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.detail-content {
  padding: 10px 0;
}

.detail-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
}

.detail-item label {
  width: 120px;
  font-weight: bold;
  color: #666;
  flex-shrink: 0;
}

.detail-item span {
  flex: 1;
  word-break: break-all;
}

.quota-info {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.quota-info h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.quota-item {
  text-align: center;
  padding: 10px;
  background: white;
  border-radius: 4px;
}

.quota-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.quota-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}
</style>
