package xiaozhi.modules.device.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import xiaozhi.common.dao.BaseDao;
import xiaozhi.modules.device.entity.DeviceActivationEntity;

/**
 * 设备激活DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Mapper
public interface DeviceActivationDao extends BaseDao<DeviceActivationEntity> {
    
    /**
     * 根据激活码获取激活记录
     * 
     * @param activationCode 激活码
     * @return 激活记录
     */
    DeviceActivationEntity getByActivationCode(@Param("activationCode") String activationCode);
    
    /**
     * 根据MAC地址获取激活记录
     * 
     * @param macAddress MAC地址
     * @return 激活记录列表
     */
    List<DeviceActivationEntity> getByMacAddress(@Param("macAddress") String macAddress);
    
    /**
     * 根据设备ID获取激活记录
     * 
     * @param deviceId 设备ID
     * @return 激活记录列表
     */
    List<DeviceActivationEntity> getByDeviceId(@Param("deviceId") String deviceId);
    
    /**
     * 根据用户ID获取激活记录
     * 
     * @param userId 用户ID
     * @return 激活记录列表
     */
    List<DeviceActivationEntity> getByUserId(@Param("userId") Long userId);
    
    /**
     * 更新激活状态
     * 
     * @param activationCode 激活码
     * @param status 激活状态
     * @param deviceId 设备ID
     * @param failureReason 失败原因
     * @param updater 更新者
     * @return 影响行数
     */
    int updateActivationStatus(
        @Param("activationCode") String activationCode,
        @Param("status") Integer status,
        @Param("deviceId") String deviceId,
        @Param("failureReason") String failureReason,
        @Param("updater") Long updater
    );
    
    /**
     * 检查激活码是否有效
     * 
     * @param activationCode 激活码
     * @return 是否有效
     */
    boolean isActivationCodeValid(@Param("activationCode") String activationCode);
    
    /**
     * 获取用户未激活的设备数量
     * 
     * @param userId 用户ID
     * @return 未激活设备数量
     */
    int getUnactivatedDeviceCount(@Param("userId") Long userId);
    
    /**
     * 获取租户激活统计
     * 
     * @param tenantId 租户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 激活统计
     */
    List<Object> getTenantActivationStatistics(
        @Param("tenantId") Long tenantId,
        @Param("startDate") String startDate,
        @Param("endDate") String endDate
    );
}
