package xiaozhi.modules.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 租户查询请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "租户查询请求")
public class TenantQueryRequestDTO {

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private String page = "1";

    @Schema(description = "每页数量", example = "10")
    @Min(value = 1, message = "每页数量必须大于0")
    private String limit = "10";

    @Schema(description = "租户编码", example = "TENANT001")
    private String tenantCode;
    
    @Schema(description = "租户名称", example = "测试租户")
    private String tenantName;
    
    @Schema(description = "企业ID", example = "1")
    private Long orgId;
    
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;
    
    @Schema(description = "创建时间开始", example = "2025-01-01")
    private String createDateStart;
    
    @Schema(description = "创建时间结束", example = "2025-01-31")
    private String createDateEnd;
    
    @Schema(description = "到期时间开始", example = "2025-01-01")
    private String expireDateStart;
    
    @Schema(description = "到期时间结束", example = "2025-12-31")
    private String expireDateEnd;
}
