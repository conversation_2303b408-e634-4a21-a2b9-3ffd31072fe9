-- 多租户系统性能优化索引脚本
-- 版本: 202501151502
-- 作者: AI Assistant
-- 描述: 为多租户系统创建优化索引，提高查询性能

-- =====================================================
-- 1. 用户表索引优化
-- =====================================================
-- 租户+用户类型复合索引（用于权限查询）
CREATE INDEX `idx_user_tenant_type` ON `sys_user` (`tenant_id`, `user_type`) 
COMMENT '租户用户类型复合索引';

-- 租户+状态复合索引（用于活跃用户查询）
CREATE INDEX `idx_user_tenant_status` ON `sys_user` (`tenant_id`, `status`) 
COMMENT '租户用户状态复合索引';

-- 用户名+租户复合索引（用于登录验证）
CREATE INDEX `idx_user_username_tenant` ON `sys_user` (`username`, `tenant_id`) 
COMMENT '用户名租户复合索引';

-- =====================================================
-- 2. 智能体表索引优化
-- =====================================================
-- 租户+用户复合索引（用于用户智能体查询）
CREATE INDEX `idx_agent_tenant_user` ON `ai_agent` (`tenant_id`, `user_id`) 
COMMENT '智能体租户用户复合索引';

-- 租户+状态复合索引（用于活跃智能体查询）
CREATE INDEX `idx_agent_tenant_status` ON `ai_agent` (`tenant_id`, `deleted`) 
COMMENT '智能体租户状态复合索引';

-- 创建时间索引（用于时间范围查询）
CREATE INDEX `idx_agent_created_at` ON `ai_agent` (`create_date`) 
COMMENT '智能体创建时间索引';

-- =====================================================
-- 3. 设备表索引优化
-- =====================================================
-- 租户+用户复合索引（用于用户设备查询）
CREATE INDEX `idx_device_tenant_user` ON `ai_device` (`tenant_id`, `user_id`) 
COMMENT '设备租户用户复合索引';

-- 租户+智能体复合索引（用于智能体设备查询）
CREATE INDEX `idx_device_tenant_agent` ON `ai_device` (`tenant_id`, `agent_id`) 
COMMENT '设备租户智能体复合索引';

-- MAC地址+租户复合索引（用于设备识别）
CREATE INDEX `idx_device_mac_tenant` ON `ai_device` (`mac_address`, `tenant_id`) 
COMMENT '设备MAC租户复合索引';

-- 最后连接时间索引（用于活跃设备查询）
CREATE INDEX `idx_device_last_connected` ON `ai_device` (`last_connected_at`) 
COMMENT '设备最后连接时间索引';

-- =====================================================
-- 4. 企业表索引优化
-- =====================================================
-- 企业状态+到期时间复合索引
CREATE INDEX `idx_org_status_expire` ON `sys_organization` (`status`, `expire_date`) 
COMMENT '企业状态到期时间复合索引';

-- 企业类型索引
CREATE INDEX `idx_org_type` ON `sys_organization` (`org_type`) 
COMMENT '企业类型索引';

-- =====================================================
-- 5. 租户表索引优化
-- =====================================================
-- 企业+状态复合索引
CREATE INDEX `idx_tenant_org_status` ON `sys_tenant` (`org_id`, `status`) 
COMMENT '租户企业状态复合索引';

-- 状态+到期时间复合索引
CREATE INDEX `idx_tenant_status_expire` ON `sys_tenant` (`status`, `expire_date`) 
COMMENT '租户状态到期时间复合索引';

-- 管理员用户索引
CREATE INDEX `idx_tenant_admin_user` ON `sys_tenant` (`admin_user_id`) 
COMMENT '租户管理员用户索引';

-- =====================================================
-- 6. 智能体分配表索引优化
-- =====================================================
-- 租户+状态复合索引（用于租户分配查询）
CREATE INDEX `idx_assignment_tenant_status` ON `ai_agent_assignment` (`tenant_id`, `status`) 
COMMENT '分配租户状态复合索引';

-- 用户+状态复合索引（用于用户分配查询）
CREATE INDEX `idx_assignment_user_status` ON `ai_agent_assignment` (`user_id`, `status`) 
COMMENT '分配用户状态复合索引';

-- 智能体+状态复合索引（用于智能体分配查询）
CREATE INDEX `idx_assignment_agent_status` ON `ai_agent_assignment` (`agent_id`, `status`) 
COMMENT '分配智能体状态复合索引';

-- 分配时间索引（用于时间范围查询）
CREATE INDEX `idx_assignment_assigned_at` ON `ai_agent_assignment` (`assigned_at`) 
COMMENT '分配时间索引';

-- =====================================================
-- 7. 智能体统计表索引优化
-- =====================================================
-- 租户+统计日期复合索引（用于租户统计查询）
CREATE INDEX `idx_agent_stats_tenant_date` ON `ai_agent_usage_statistics` (`tenant_id`, `statistics_date`) 
COMMENT '智能体统计租户日期复合索引';

-- 智能体+统计日期复合索引（用于智能体统计查询）
CREATE INDEX `idx_agent_stats_agent_date` ON `ai_agent_usage_statistics` (`agent_id`, `statistics_date`) 
COMMENT '智能体统计智能体日期复合索引';

-- 用户+统计日期复合索引（用于用户统计查询）
CREATE INDEX `idx_agent_stats_user_date` ON `ai_agent_usage_statistics` (`user_id`, `statistics_date`) 
COMMENT '智能体统计用户日期复合索引';

-- 使用次数索引（用于排行查询）
CREATE INDEX `idx_agent_stats_usage_count` ON `ai_agent_usage_statistics` (`usage_count`) 
COMMENT '智能体统计使用次数索引';

-- =====================================================
-- 8. 设备激活表索引优化
-- =====================================================
-- 租户+激活状态复合索引
CREATE INDEX `idx_activation_tenant_status` ON `ai_device_activation` (`tenant_id`, `activation_status`) 
COMMENT '激活租户状态复合索引';

-- 用户+激活状态复合索引
CREATE INDEX `idx_activation_user_status` ON `ai_device_activation` (`user_id`, `activation_status`) 
COMMENT '激活用户状态复合索引';

-- 激活码过期时间索引
CREATE INDEX `idx_activation_code_expired` ON `ai_device_activation` (`code_expired_at`) 
COMMENT '激活码过期时间索引';

-- 激活时间索引
CREATE INDEX `idx_activation_activated_at` ON `ai_device_activation` (`activated_at`) 
COMMENT '激活时间索引';

-- =====================================================
-- 9. 设备统计表索引优化
-- =====================================================
-- 租户+统计日期复合索引
CREATE INDEX `idx_device_stats_tenant_date` ON `ai_device_usage_statistics` (`tenant_id`, `statistics_date`) 
COMMENT '设备统计租户日期复合索引';

-- 设备+统计日期复合索引
CREATE INDEX `idx_device_stats_device_date` ON `ai_device_usage_statistics` (`device_id`, `statistics_date`) 
COMMENT '设备统计设备日期复合索引';

-- 用户+统计日期复合索引
CREATE INDEX `idx_device_stats_user_date` ON `ai_device_usage_statistics` (`user_id`, `statistics_date`) 
COMMENT '设备统计用户日期复合索引';

-- 智能体+统计日期复合索引
CREATE INDEX `idx_device_stats_agent_date` ON `ai_device_usage_statistics` (`agent_id`, `statistics_date`) 
COMMENT '设备统计智能体日期复合索引';

-- 连接次数索引（用于活跃设备排行）
CREATE INDEX `idx_device_stats_connection_count` ON `ai_device_usage_statistics` (`connection_count`) 
COMMENT '设备统计连接次数索引';

-- 在线时长索引（用于使用时长排行）
CREATE INDEX `idx_device_stats_online_duration` ON `ai_device_usage_statistics` (`total_online_duration`) 
COMMENT '设备统计在线时长索引';

-- =====================================================
-- 10. 索引使用情况分析
-- =====================================================
-- 查看索引使用情况的SQL（供DBA参考）

-- 查看表的索引信息
-- SHOW INDEX FROM sys_user;
-- SHOW INDEX FROM ai_agent;
-- SHOW INDEX FROM ai_device;

-- 查看索引使用统计
-- SELECT 
--     TABLE_SCHEMA,
--     TABLE_NAME,
--     INDEX_NAME,
--     CARDINALITY,
--     SUB_PART,
--     PACKED,
--     NULLABLE,
--     INDEX_TYPE,
--     COMMENT
-- FROM INFORMATION_SCHEMA.STATISTICS 
-- WHERE TABLE_SCHEMA = 'xiaozhi_esp32_server'
-- ORDER BY TABLE_NAME, INDEX_NAME;

-- 分析表统计信息
-- ANALYZE TABLE sys_user, ai_agent, ai_device, sys_organization, sys_tenant,
--                ai_agent_assignment, ai_agent_usage_statistics, 
--                ai_device_activation, ai_device_usage_statistics;

-- =====================================================
-- 11. 性能监控建议
-- =====================================================
-- 建议定期执行以下查询来监控性能：

-- 1. 检查慢查询
-- SHOW VARIABLES LIKE 'slow_query_log';
-- SHOW VARIABLES LIKE 'long_query_time';

-- 2. 检查索引效率
-- SHOW STATUS LIKE 'Handler_read%';

-- 3. 检查表大小
-- SELECT 
--     table_name,
--     ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
-- FROM information_schema.TABLES 
-- WHERE table_schema = 'xiaozhi_esp32_server'
-- ORDER BY (data_length + index_length) DESC;

-- =====================================================
-- 索引创建完成
-- =====================================================
SELECT 'Multi-tenant indexes created successfully!' as status;
