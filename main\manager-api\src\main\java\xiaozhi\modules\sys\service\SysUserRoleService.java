package xiaozhi.modules.sys.service;

import java.util.List;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.sys.entity.SysUserRoleEntity;

/**
 * 用户角色关联Service
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface SysUserRoleService extends BaseService<SysUserRoleEntity> {
    
    /**
     * 根据用户ID获取角色ID列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getRoleIdsByUserId(Long userId);
    
    /**
     * 根据角色ID获取用户ID列表
     * 
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> getUserIdsByRoleId(Long roleId);
    
    /**
     * 根据用户ID删除角色关联
     * 
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(Long userId);
    
    /**
     * 根据角色ID删除用户关联
     * 
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteByRoleId(Long roleId);
    
    /**
     * 根据用户ID和角色ID删除关联
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteByUserIdAndRoleId(Long userId, Long roleId);
    
    /**
     * 批量插入用户角色关联
     * 
     * @param userRoles 用户角色关联列表
     * @return 插入数量
     */
    int batchInsert(List<SysUserRoleEntity> userRoles);
}
