<template>
  <div class="device-memory-test">
    <h3>设备记忆功能测试</h3>
    
    <div class="test-section">
      <h4>1. 测试记忆文本截断</h4>
      <div class="memory-demo">
        <div class="memory-cell">
          <el-tooltip 
            v-if="testMemory" 
            :content="testMemory" 
            placement="top" 
            effect="dark"
            :open-delay="300">
            <span 
              class="memory-text" 
              @click="showFullMemory(testMemory)">
              {{ truncateMemory(testMemory) }}
            </span>
          </el-tooltip>
          <span v-else class="no-memory">暂无记忆</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h4>2. 测试API调用</h4>
      <el-input 
        v-model="testMacAddress" 
        placeholder="输入MAC地址测试"
        style="width: 300px; margin-right: 10px;">
      </el-input>
      <el-button @click="testGetMemory" type="primary">获取记忆</el-button>
      <div v-if="apiResult" class="api-result">
        <p><strong>API结果:</strong></p>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </div>

    <!-- 记忆详情弹窗 -->
    <el-dialog
      title="设备记忆详情"
      :visible.sync="memoryDialogVisible"
      width="60%">
      <div class="memory-content">
        <el-input
          type="textarea"
          :rows="10"
          v-model="currentMemoryContent"
          readonly
          placeholder="暂无记忆内容">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="memoryDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Api from '@/apis/api';

export default {
  name: 'DeviceMemoryTest',
  data() {
    return {
      testMemory: '这是一个很长的设备记忆内容，用来测试文本截断功能。用户喜欢听音乐，经常询问天气信息，偏爱中式菜肴，每天晚上9点运动，周末喜欢看电影。',
      testMacAddress: 'AA:BB:CC:DD:EE:FF',
      memoryDialogVisible: false,
      currentMemoryContent: '',
      memoryMaxLength: 50,
      apiResult: null
    };
  },
  methods: {
    truncateMemory(memory) {
      if (!memory) return '';
      if (memory.length <= this.memoryMaxLength) {
        return memory;
      }
      return memory.substring(0, this.memoryMaxLength) + '...';
    },
    showFullMemory(memory) {
      this.currentMemoryContent = memory || '';
      this.memoryDialogVisible = true;
    },
    testGetMemory() {
      if (!this.testMacAddress) {
        this.$message.warning('请输入MAC地址');
        return;
      }
      
      Api.device.getDeviceMemory(this.testMacAddress, ({ data }) => {
        this.apiResult = data;
        if (data.code === 0) {
          this.$message.success('获取记忆成功');
        } else {
          this.$message.error(data.msg || '获取记忆失败');
        }
      });
    }
  }
};
</script>

<style scoped>
.device-memory-test {
  padding: 20px;
  max-width: 800px;
}

.test-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.memory-demo {
  margin-top: 10px;
  padding: 10px;
  background-color: #F5F7FA;
  border-radius: 4px;
}

.memory-cell {
  padding: 4px 8px;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.memory-text {
  color: #606266;
  cursor: pointer;
  transition: color 0.3s ease;
  word-break: break-all;
  line-height: 1.4;
  display: block;
  text-align: left;
}

.memory-text:hover {
  color: #409EFF;
  text-decoration: underline;
}

.no-memory {
  color: #C0C4CC;
  font-style: italic;
  font-size: 12px;
}

.api-result {
  margin-top: 15px;
  padding: 10px;
  background-color: #F5F7FA;
  border-radius: 4px;
}

.api-result pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
}

.memory-content {
  padding: 10px 0;
}

.memory-content .el-textarea__inner {
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  line-height: 1.6;
  resize: none;
}

.dialog-footer {
  text-align: center;
}
</style>
