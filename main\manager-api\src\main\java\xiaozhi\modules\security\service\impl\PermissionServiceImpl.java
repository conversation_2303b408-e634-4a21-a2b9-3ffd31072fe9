package xiaozhi.modules.security.service.impl;

import java.util.Set;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import xiaozhi.modules.security.service.PermissionService;
import xiaozhi.modules.sys.service.SysPermissionService;

/**
 * 权限服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Service
@AllArgsConstructor
public class PermissionServiceImpl implements PermissionService {
    
    private final SysPermissionService sysPermissionService;
    
    @Override
    public Set<String> getUserPermissions(Long userId) {
        return sysPermissionService.getUserPermissions(userId);
    }
    
    @Override
    public boolean hasPermission(Long userId, String permission) {
        return sysPermissionService.hasPermission(userId, permission);
    }
    
    @Override
    public boolean hasTenantPermission(Long userId, String permission, Long tenantId) {
        return sysPermissionService.hasTenantPermission(userId, permission, tenantId);
    }
    
    @Override
    public boolean hasAnyPermission(Long userId, String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return false;
        }
        
        for (String permission : permissions) {
            if (hasPermission(userId, permission)) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public boolean hasAllPermissions(Long userId, String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return true;
        }
        
        for (String permission : permissions) {
            if (!hasPermission(userId, permission)) {
                return false;
            }
        }
        
        return true;
    }
}
