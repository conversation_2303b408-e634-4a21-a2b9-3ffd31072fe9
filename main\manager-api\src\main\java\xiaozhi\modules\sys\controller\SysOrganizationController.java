package xiaozhi.modules.sys.controller;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.page.PageData;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.sys.dto.OrganizationCreateRequestDTO;
import xiaozhi.modules.sys.dto.OrganizationDetailResponseDTO;
import xiaozhi.modules.sys.dto.OrganizationQueryRequestDTO;
import xiaozhi.modules.sys.dto.OrganizationUpdateRequestDTO;
import xiaozhi.modules.sys.entity.SysOrganizationEntity;
import xiaozhi.modules.sys.service.SysOrganizationService;

import javax.validation.Valid;

/**
 * 企业组织管理Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@RestController
@RequestMapping("/sys/organization")
@Tag(name = "企业组织管理", description = "企业组织的增删改查、状态管理、统计查询等功能")
public class SysOrganizationController {
    
    private final SysOrganizationService sysOrganizationService;
    
    @GetMapping("/list")
    @Operation(
        operationId = "listOrganizations",
        summary = "获取企业列表",
        description = "获取系统中所有企业的列表信息，支持分页查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "organization:view:*", description = "查看企业列表")
    public Result<List<SysOrganizationEntity>> list() {
        List<SysOrganizationEntity> organizations = sysOrganizationService.list();
        return new Result<List<SysOrganizationEntity>>().ok(organizations);
    }
    
    @GetMapping("/{id}")
    @Operation(
        operationId = "getOrganizationInfo",
        summary = "获取企业信息",
        description = "根据企业ID获取企业的基本信息"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "企业不存在")
    })
    @RequiresPermission(value = "organization:view:*", description = "查看企业信息")
    public Result<SysOrganizationEntity> info(
            @Parameter(description = "企业ID", required = true, example = "1")
            @PathVariable Long id) {
        SysOrganizationEntity organization = sysOrganizationService.getById(id);
        return new Result<SysOrganizationEntity>().ok(organization);
    }
    
    @GetMapping("/type/{orgType}")
    @Operation(
        operationId = "getOrganizationsByType",
        summary = "根据企业类型获取企业列表",
        description = "获取指定类型的企业列表，用于分类查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "organization:view:*", description = "按类型查看企业")
    public Result<List<SysOrganizationEntity>> getByType(
            @Parameter(description = "企业类型", required = true, example = "1",
                schema = @Schema(allowableValues = {"1", "2"}))
            @PathVariable Integer orgType) {
        List<SysOrganizationEntity> organizations = sysOrganizationService.getOrganizationsByType(orgType);
        return new Result<List<SysOrganizationEntity>>().ok(organizations);
    }
    
    @GetMapping("/expiring/{days}")
    @Operation(
        operationId = "getExpiringOrganizations",
        summary = "获取即将到期的企业",
        description = "获取在指定天数内即将到期的企业列表，用于到期提醒和续费管理"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "organization:view:*", description = "查看到期企业")
    public Result<List<SysOrganizationEntity>> getExpiringOrganizations(
            @Parameter(description = "天数", required = true, example = "30",
                schema = @Schema(minimum = "1", maximum = "365"))
            @PathVariable Integer days) {
        List<SysOrganizationEntity> organizations = sysOrganizationService.getExpiringOrganizations(days);
        return new Result<List<SysOrganizationEntity>>().ok(organizations);
    }
    
    @PostMapping("/page")
    @Operation(
        operationId = "pageOrganizations",
        summary = "分页查询企业",
        description = "根据查询条件分页获取企业详情列表，支持多种查询条件组合"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "organization:view:*", description = "分页查询企业")
    public Result<PageData<OrganizationDetailResponseDTO>> page(
            @Parameter(description = "查询条件", required = true)
            @RequestBody @Valid OrganizationQueryRequestDTO queryRequest) {
        PageData<OrganizationDetailResponseDTO> page = sysOrganizationService.pageWithDetails(queryRequest);
        return new Result<PageData<OrganizationDetailResponseDTO>>().ok(page);
    }

    @GetMapping("/detail/{id}")
    @Operation(
        operationId = "getOrganizationDetail",
        summary = "获取企业详情",
        description = "根据企业ID获取企业的详细信息，包括统计数据、租户信息等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "企业不存在")
    })
    @RequiresPermission(value = "organization:view:*", description = "查看企业详情")
    public Result<OrganizationDetailResponseDTO> detail(
            @Parameter(description = "企业ID", required = true, example = "1")
            @PathVariable Long id) {
        OrganizationDetailResponseDTO detail = sysOrganizationService.getOrganizationDetail(id);
        return new Result<OrganizationDetailResponseDTO>().ok(detail);
    }

    @PostMapping
    @Operation(
        operationId = "createOrganization",
        summary = "创建企业",
        description = "创建新的企业，需要指定企业基本信息和联系方式"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "organization:create:*", description = "创建企业")
    public Result<Void> create(
            @Parameter(description = "创建请求", required = true)
            @RequestBody @Valid OrganizationCreateRequestDTO createRequest) {
        sysOrganizationService.createOrganization(createRequest);
        return new Result<>();
    }

    @PutMapping
    @Operation(
        operationId = "updateOrganization",
        summary = "更新企业",
        description = "更新企业信息，包括基本信息、联系方式等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "404", description = "企业不存在")
    })
    @RequiresPermission(value = "organization:update:*", description = "更新企业")
    public Result<Void> update(
            @Parameter(description = "更新请求", required = true)
            @RequestBody @Valid OrganizationUpdateRequestDTO updateRequest) {
        sysOrganizationService.updateOrganization(updateRequest);
        return new Result<>();
    }

    @DeleteMapping("/{id}")
    @Operation(
        operationId = "deleteOrganization",
        summary = "删除企业",
        description = "删除指定的企业，删除前会检查是否有关联数据"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "企业不存在"),
        @ApiResponse(responseCode = "400", description = "企业下还有关联数据，无法删除")
    })
    @RequiresPermission(value = "organization:delete:*", description = "删除企业")
    public Result<Void> delete(
            @Parameter(description = "企业ID", required = true, example = "1")
            @PathVariable Long id) {
        sysOrganizationService.deleteOrganization(id);
        return new Result<>();
    }

    @PutMapping("/{id}/status/{status}")
    @Operation(
        operationId = "updateOrganizationStatus",
        summary = "更新企业状态",
        description = "启用或禁用指定的企业"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "企业不存在")
    })
    @RequiresPermission(value = "organization:update:*", description = "更新企业状态")
    public Result<Void> updateStatus(
            @Parameter(description = "企业ID", required = true, example = "1")
            @PathVariable Long id,
            @Parameter(description = "状态", required = true, example = "1",
                schema = @Schema(allowableValues = {"0", "1"}))
            @PathVariable Integer status) {
        sysOrganizationService.updateOrganizationStatus(id, status);
        return new Result<>();
    }

    @GetMapping("/{id}/statistics")
    @Operation(
        operationId = "getOrganizationStatistics",
        summary = "获取企业统计信息",
        description = "获取企业的租户数、用户总数、设备总数等统计信息"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "企业不存在")
    })
    @RequiresPermission(value = "organization:view:*", description = "查看企业统计")
    public Result<Map<String, Object>> statistics(
            @Parameter(description = "企业ID", required = true, example = "1")
            @PathVariable Long id) {
        Map<String, Object> statistics = sysOrganizationService.getOrganizationStatistics(id);
        return new Result<Map<String, Object>>().ok(statistics);
    }
}
