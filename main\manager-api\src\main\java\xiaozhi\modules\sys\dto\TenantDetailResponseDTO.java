package xiaozhi.modules.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 租户详情响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "租户详情响应")
public class TenantDetailResponseDTO {
    
    @Schema(description = "租户ID", example = "1")
    private Long id;
    
    @Schema(description = "租户编码", example = "TENANT001")
    private String tenantCode;
    
    @Schema(description = "租户名称", example = "测试租户")
    private String tenantName;
    
    @Schema(description = "企业ID", example = "1")
    private Long orgId;
    
    @Schema(description = "企业名称", example = "测试企业")
    private String orgName;
    
    @Schema(description = "租户管理员用户ID", example = "1")
    private Long adminUserId;
    
    @Schema(description = "租户管理员用户名", example = "admin")
    private String adminUsername;
    
    @Schema(description = "租户管理员真实姓名", example = "管理员")
    private String adminRealName;
    
    @Schema(description = "最大用户数", example = "100")
    private Integer maxUsers;
    
    @Schema(description = "最大设备数", example = "1000")
    private Integer maxDevices;
    
    @Schema(description = "最大智能体数", example = "50")
    private Integer maxAgents;
    
    @Schema(description = "当前用户数", example = "10")
    private Long currentUsers;
    
    @Schema(description = "当前设备数", example = "50")
    private Long currentDevices;
    
    @Schema(description = "当前智能体数", example = "5")
    private Long currentAgents;
    
    @Schema(description = "状态", example = "1")
    private Integer status;
    
    @Schema(description = "状态名称", example = "启用")
    private String statusName;
    
    @Schema(description = "到期时间", example = "2025-12-31 23:59:59")
    private Date expireDate;
    
    @Schema(description = "是否即将到期", example = "false")
    private Boolean isExpiring;
    
    @Schema(description = "剩余天数", example = "365")
    private Integer remainingDays;
    
    @Schema(description = "备注", example = "测试租户备注")
    private String remark;
    
    @Schema(description = "创建时间", example = "2025-01-15 10:00:00")
    private Date createDate;
    
    @Schema(description = "更新时间", example = "2025-01-15 10:00:00")
    private Date updateDate;
    
    @Schema(description = "创建者", example = "1")
    private Long creator;
    
    @Schema(description = "更新者", example = "1")
    private Long updater;
}
