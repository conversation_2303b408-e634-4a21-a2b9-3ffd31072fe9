import store from '@/store'
import { PERMISSION_DEFINITIONS } from '@/config/permissions'

/**
 * 检查用户是否有指定权限
 * @param {String} permission 权限字符串，格式：domain:action:instance
 * @returns {Boolean}
 */
export function hasPermission(permission) {
  const permissions = store.getters.permissions || []
  
  // 超级管理员拥有所有权限
  if (permissions.includes('*:*:*')) {
    return true
  }
  
  // 检查精确匹配
  if (permissions.includes(permission)) {
    return true
  }
  
  // 检查通配符权限
  const parts = permission.split(':')
  if (parts.length === 3) {
    const [domain, action, instance] = parts
    
    // 检查 domain:action:* 权限
    if (permissions.includes(`${domain}:${action}:*`)) {
      return true
    }
    
    // 检查 domain:*:* 权限
    if (permissions.includes(`${domain}:*:*`)) {
      return true
    }
  }
  
  return false
}

/**
 * 检查用户是否有任意一个权限
 * @param {Array} permissions 权限数组
 * @returns {Boolean}
 */
export function hasAnyPermission(permissions) {
  if (!Array.isArray(permissions) || permissions.length === 0) {
    return false
  }
  
  return permissions.some(permission => hasPermission(permission))
}

/**
 * 检查用户是否有所有权限
 * @param {Array} permissions 权限数组
 * @returns {Boolean}
 */
export function hasAllPermissions(permissions) {
  if (!Array.isArray(permissions) || permissions.length === 0) {
    return true
  }
  
  return permissions.every(permission => hasPermission(permission))
}

/**
 * 检查用户是否为平台管理员
 * @returns {Boolean}
 */
export function isPlatformAdmin() {
  return hasPermission('*:*:*') || hasPermission('organization:view:*')
}

/**
 * 检查用户是否为租户管理员
 * @returns {Boolean}
 */
export function isTenantAdmin() {
  return hasPermission('user:create:tenant') || hasPermission('tenant:manage:*')
}

/**
 * 检查用户类型
 * @returns {Number} 1:平台管理员 2:租户管理员 3:普通用户
 */
export function getUserType() {
  if (isPlatformAdmin()) {
    return 1
  } else if (isTenantAdmin()) {
    return 2
  } else {
    return 3
  }
}

/**
 * 根据权限过滤菜单
 * @param {Array} menus 菜单数组
 * @param {Array} userPermissions 用户权限数组
 * @returns {Array} 过滤后的菜单
 */
export function filterMenuByPermission(menus, userPermissions) {
  if (!Array.isArray(menus)) {
    return []
  }
  
  return menus.filter(menu => {
    // 如果菜单有权限要求
    if (menu.meta && menu.meta.permission) {
      return hasPermission(menu.meta.permission)
    }
    
    // 如果菜单有子菜单，递归过滤
    if (menu.children && menu.children.length > 0) {
      menu.children = filterMenuByPermission(menu.children, userPermissions)
      return menu.children.length > 0
    }
    
    // 没有权限要求的菜单默认显示
    return true
  })
}

/**
 * 权限常量定义 - 从配置文件生成
 */
export const PERMISSIONS = {}

// 动态生成权限常量
Object.keys(PERMISSION_DEFINITIONS).forEach(domain => {
  PERMISSIONS[domain] = {}
  Object.keys(PERMISSION_DEFINITIONS[domain]).forEach(action => {
    PERMISSIONS[domain][action] = PERMISSION_DEFINITIONS[domain][action].code
  })
})

/**
 * 权限检查的便捷方法
 */
export const PermissionChecker = {
  // 组织管理权限检查
  canViewOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.VIEW_ALL),
  canCreateOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.CREATE),
  canUpdateOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.UPDATE),
  canDeleteOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.DELETE),
  canManageOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.MANAGE),

  // 租户管理权限检查
  canViewTenant: () => hasAnyPermission([PERMISSIONS.TENANT.VIEW_ALL, PERMISSIONS.TENANT.VIEW_TENANT]),
  canCreateTenant: () => hasPermission(PERMISSIONS.TENANT.CREATE),
  canUpdateTenant: () => hasPermission(PERMISSIONS.TENANT.UPDATE),
  canDeleteTenant: () => hasPermission(PERMISSIONS.TENANT.DELETE),
  canManageTenant: () => hasPermission(PERMISSIONS.TENANT.MANAGE),

  // 用户管理权限检查
  canViewUser: () => hasAnyPermission([PERMISSIONS.USER.VIEW_ALL, PERMISSIONS.USER.VIEW_TENANT]),
  canCreateUser: () => hasAnyPermission([PERMISSIONS.USER.CREATE_ALL, PERMISSIONS.USER.CREATE_TENANT]),
  canUpdateUser: () => hasAnyPermission([PERMISSIONS.USER.UPDATE_ALL, PERMISSIONS.USER.UPDATE_TENANT]),
  canDeleteUser: () => hasAnyPermission([PERMISSIONS.USER.DELETE_ALL, PERMISSIONS.USER.DELETE_TENANT]),
  canResetPassword: () => hasPermission(PERMISSIONS.USER.RESET_PASSWORD),
  canChangeUserStatus: () => hasPermission(PERMISSIONS.USER.CHANGE_STATUS),

  // 权限管理权限检查
  canViewPermission: () => hasAnyPermission([PERMISSIONS.PERMISSION.VIEW_ALL, PERMISSIONS.PERMISSION.VIEW_TENANT]),
  canAssignPermission: () => hasAnyPermission([PERMISSIONS.PERMISSION.ASSIGN_ALL, PERMISSIONS.PERMISSION.ASSIGN_TENANT]),
  canRevokePermission: () => hasPermission(PERMISSIONS.PERMISSION.REVOKE),
  canManagePermission: () => hasPermission(PERMISSIONS.PERMISSION.MANAGE),

  // 智能体管理权限检查
  canViewAgent: () => hasAnyPermission([PERMISSIONS.AGENT.VIEW_ALL, PERMISSIONS.AGENT.VIEW_TENANT]),
  canCreateAgent: () => hasAnyPermission([PERMISSIONS.AGENT.CREATE_ALL, PERMISSIONS.AGENT.CREATE_TENANT]),
  canUpdateAgent: () => hasAnyPermission([PERMISSIONS.AGENT.UPDATE_ALL, PERMISSIONS.AGENT.UPDATE_TENANT]),
  canDeleteAgent: () => hasAnyPermission([PERMISSIONS.AGENT.DELETE_ALL, PERMISSIONS.AGENT.DELETE_TENANT]),
  canAssignAgent: () => hasPermission(PERMISSIONS.AGENT.ASSIGN),

  // 设备管理权限检查
  canViewDevice: () => hasAnyPermission([PERMISSIONS.DEVICE.VIEW_ALL, PERMISSIONS.DEVICE.VIEW_TENANT]),
  canCreateDevice: () => hasPermission(PERMISSIONS.DEVICE.CREATE),
  canUpdateDevice: () => hasPermission(PERMISSIONS.DEVICE.UPDATE),
  canDeleteDevice: () => hasPermission(PERMISSIONS.DEVICE.DELETE),
  canBindDevice: () => hasAnyPermission([PERMISSIONS.DEVICE.BIND_ALL, PERMISSIONS.DEVICE.BIND_TENANT]),
  canUnbindDevice: () => hasAnyPermission([PERMISSIONS.DEVICE.UNBIND_ALL, PERMISSIONS.DEVICE.UNBIND_TENANT]),
  canActivateDevice: () => hasPermission(PERMISSIONS.DEVICE.ACTIVATE)
}
