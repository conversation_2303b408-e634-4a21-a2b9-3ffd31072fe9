import store from '@/store'

/**
 * 检查用户是否有指定权限
 * @param {String} permission 权限字符串，格式：domain:action:instance
 * @returns {Boolean}
 */
export function hasPermission(permission) {
  const permissions = store.getters.permissions || []
  
  // 超级管理员拥有所有权限
  if (permissions.includes('*:*:*')) {
    return true
  }
  
  // 检查精确匹配
  if (permissions.includes(permission)) {
    return true
  }
  
  // 检查通配符权限
  const parts = permission.split(':')
  if (parts.length === 3) {
    const [domain, action, instance] = parts
    
    // 检查 domain:action:* 权限
    if (permissions.includes(`${domain}:${action}:*`)) {
      return true
    }
    
    // 检查 domain:*:* 权限
    if (permissions.includes(`${domain}:*:*`)) {
      return true
    }
  }
  
  return false
}

/**
 * 检查用户是否有任意一个权限
 * @param {Array} permissions 权限数组
 * @returns {Boolean}
 */
export function hasAnyPermission(permissions) {
  if (!Array.isArray(permissions) || permissions.length === 0) {
    return false
  }
  
  return permissions.some(permission => hasPermission(permission))
}

/**
 * 检查用户是否有所有权限
 * @param {Array} permissions 权限数组
 * @returns {Boolean}
 */
export function hasAllPermissions(permissions) {
  if (!Array.isArray(permissions) || permissions.length === 0) {
    return true
  }
  
  return permissions.every(permission => hasPermission(permission))
}

/**
 * 检查用户是否为平台管理员
 * @returns {Boolean}
 */
export function isPlatformAdmin() {
  return hasPermission('*:*:*') || hasPermission('organization:view:*')
}

/**
 * 检查用户是否为租户管理员
 * @returns {Boolean}
 */
export function isTenantAdmin() {
  return hasPermission('user:create:tenant') || hasPermission('tenant:manage:*')
}

/**
 * 检查用户类型
 * @returns {Number} 1:平台管理员 2:租户管理员 3:普通用户
 */
export function getUserType() {
  if (isPlatformAdmin()) {
    return 1
  } else if (isTenantAdmin()) {
    return 2
  } else {
    return 3
  }
}

/**
 * 根据权限过滤菜单
 * @param {Array} menus 菜单数组
 * @param {Array} userPermissions 用户权限数组
 * @returns {Array} 过滤后的菜单
 */
export function filterMenuByPermission(menus, userPermissions) {
  if (!Array.isArray(menus)) {
    return []
  }
  
  return menus.filter(menu => {
    // 如果菜单有权限要求
    if (menu.meta && menu.meta.permission) {
      return hasPermission(menu.meta.permission)
    }
    
    // 如果菜单有子菜单，递归过滤
    if (menu.children && menu.children.length > 0) {
      menu.children = filterMenuByPermission(menu.children, userPermissions)
      return menu.children.length > 0
    }
    
    // 没有权限要求的菜单默认显示
    return true
  })
}

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 组织管理权限
  ORGANIZATION: {
    VIEW: 'organization:view:*',
    CREATE: 'organization:create:*',
    UPDATE: 'organization:update:*',
    DELETE: 'organization:delete:*',
    MANAGE: 'organization:manage:*'
  },
  
  // 租户管理权限
  TENANT: {
    VIEW: 'tenant:view:*',
    CREATE: 'tenant:create:*',
    UPDATE: 'tenant:update:*',
    DELETE: 'tenant:delete:*',
    MANAGE: 'tenant:manage:*',
    VIEW_TENANT: 'tenant:view:tenant'
  },
  
  // 用户管理权限
  USER: {
    VIEW: 'user:view:*',
    CREATE: 'user:create:*',
    UPDATE: 'user:update:*',
    DELETE: 'user:delete:*',
    RESET_PASSWORD: 'user:reset-password:*',
    CHANGE_STATUS: 'user:change-status:*',
    VIEW_TENANT: 'user:view:tenant',
    CREATE_TENANT: 'user:create:tenant',
    UPDATE_TENANT: 'user:update:tenant',
    DELETE_TENANT: 'user:delete:tenant'
  },
  
  // 权限管理权限
  PERMISSION: {
    VIEW: 'permission:view:*',
    ASSIGN: 'permission:assign:*',
    REVOKE: 'permission:revoke:*',
    MANAGE: 'permission:manage:*',
    VIEW_TENANT: 'permission:view:tenant',
    ASSIGN_TENANT: 'permission:assign:tenant'
  },
  
  // 智能体管理权限
  AGENT: {
    VIEW: 'agent:view:*',
    CREATE: 'agent:create:*',
    UPDATE: 'agent:update:*',
    DELETE: 'agent:delete:*',
    ASSIGN: 'agent:assign:*',
    VIEW_TENANT: 'agent:view:tenant',
    CREATE_TENANT: 'agent:create:tenant',
    UPDATE_TENANT: 'agent:update:tenant',
    DELETE_TENANT: 'agent:delete:tenant'
  },
  
  // 设备管理权限
  DEVICE: {
    VIEW: 'device:view:*',
    CREATE: 'device:create:*',
    UPDATE: 'device:update:*',
    DELETE: 'device:delete:*',
    BIND: 'device:bind:*',
    UNBIND: 'device:unbind:*',
    ACTIVATE: 'device:activate:*',
    VIEW_TENANT: 'device:view:tenant',
    BIND_TENANT: 'device:bind:tenant',
    UNBIND_TENANT: 'device:unbind:tenant'
  },
  
  // 系统管理权限
  SYSTEM: {
    CONFIG: 'system:config:*',
    MONITOR: 'system:monitor:*',
    LOG: 'system:log:*'
  }
}

/**
 * 权限检查的便捷方法
 */
export const PermissionChecker = {
  // 组织管理权限检查
  canViewOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.VIEW),
  canCreateOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.CREATE),
  canUpdateOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.UPDATE),
  canDeleteOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.DELETE),
  canManageOrganization: () => hasPermission(PERMISSIONS.ORGANIZATION.MANAGE),
  
  // 租户管理权限检查
  canViewTenant: () => hasAnyPermission([PERMISSIONS.TENANT.VIEW, PERMISSIONS.TENANT.VIEW_TENANT]),
  canCreateTenant: () => hasPermission(PERMISSIONS.TENANT.CREATE),
  canUpdateTenant: () => hasPermission(PERMISSIONS.TENANT.UPDATE),
  canDeleteTenant: () => hasPermission(PERMISSIONS.TENANT.DELETE),
  canManageTenant: () => hasPermission(PERMISSIONS.TENANT.MANAGE),
  
  // 用户管理权限检查
  canViewUser: () => hasAnyPermission([PERMISSIONS.USER.VIEW, PERMISSIONS.USER.VIEW_TENANT]),
  canCreateUser: () => hasAnyPermission([PERMISSIONS.USER.CREATE, PERMISSIONS.USER.CREATE_TENANT]),
  canUpdateUser: () => hasAnyPermission([PERMISSIONS.USER.UPDATE, PERMISSIONS.USER.UPDATE_TENANT]),
  canDeleteUser: () => hasAnyPermission([PERMISSIONS.USER.DELETE, PERMISSIONS.USER.DELETE_TENANT]),
  canResetPassword: () => hasPermission(PERMISSIONS.USER.RESET_PASSWORD),
  canChangeUserStatus: () => hasPermission(PERMISSIONS.USER.CHANGE_STATUS),
  
  // 权限管理权限检查
  canViewPermission: () => hasAnyPermission([PERMISSIONS.PERMISSION.VIEW, PERMISSIONS.PERMISSION.VIEW_TENANT]),
  canAssignPermission: () => hasAnyPermission([PERMISSIONS.PERMISSION.ASSIGN, PERMISSIONS.PERMISSION.ASSIGN_TENANT]),
  canRevokePermission: () => hasPermission(PERMISSIONS.PERMISSION.REVOKE),
  canManagePermission: () => hasPermission(PERMISSIONS.PERMISSION.MANAGE),
  
  // 智能体管理权限检查
  canViewAgent: () => hasAnyPermission([PERMISSIONS.AGENT.VIEW, PERMISSIONS.AGENT.VIEW_TENANT]),
  canCreateAgent: () => hasAnyPermission([PERMISSIONS.AGENT.CREATE, PERMISSIONS.AGENT.CREATE_TENANT]),
  canUpdateAgent: () => hasAnyPermission([PERMISSIONS.AGENT.UPDATE, PERMISSIONS.AGENT.UPDATE_TENANT]),
  canDeleteAgent: () => hasAnyPermission([PERMISSIONS.AGENT.DELETE, PERMISSIONS.AGENT.DELETE_TENANT]),
  canAssignAgent: () => hasPermission(PERMISSIONS.AGENT.ASSIGN),
  
  // 设备管理权限检查
  canViewDevice: () => hasAnyPermission([PERMISSIONS.DEVICE.VIEW, PERMISSIONS.DEVICE.VIEW_TENANT]),
  canCreateDevice: () => hasPermission(PERMISSIONS.DEVICE.CREATE),
  canUpdateDevice: () => hasPermission(PERMISSIONS.DEVICE.UPDATE),
  canDeleteDevice: () => hasPermission(PERMISSIONS.DEVICE.DELETE),
  canBindDevice: () => hasAnyPermission([PERMISSIONS.DEVICE.BIND, PERMISSIONS.DEVICE.BIND_TENANT]),
  canUnbindDevice: () => hasAnyPermission([PERMISSIONS.DEVICE.UNBIND, PERMISSIONS.DEVICE.UNBIND_TENANT]),
  canActivateDevice: () => hasPermission(PERMISSIONS.DEVICE.ACTIVATE)
}
