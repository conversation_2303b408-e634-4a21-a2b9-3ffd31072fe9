package xiaozhi.modules.security.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 统一登录请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "登录请求")
public class LoginRequestDTO {
    
    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "admin")
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @Schema(description = "验证码ID", example = "uuid-1234")
    private String captchaId;
    
    @Schema(description = "验证码", example = "abcd")
    private String captcha;
    
    @Schema(description = "手机验证码", example = "123456")
    private String mobileCaptcha;
    
    @Schema(description = "记住我", example = "true")
    private Boolean rememberMe = false;
}
