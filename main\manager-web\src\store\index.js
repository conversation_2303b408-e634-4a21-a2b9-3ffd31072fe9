import { goToPage } from "@/utils";
import Vue from 'vue';
import Vuex from 'vuex';
import Api from '../apis/api';
import Constant from '../utils/constant';
import permissionApi from '../apis/module/permission';

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    token: '',
    userInfo: {}, // 用户信息存储
    permissions: [], // 用户权限列表
    roles: [], // 用户角色列表
    isSuperAdmin: false, // 保留兼容性，但不再使用
    pubConfig: { // 公共配置存储
      version: '',
      beianIcpNum: 'null',
      beianGaNum: 'null',
      allowUserRegister: false
    }
  },
  getters: {
    getToken(state) {
      if (!state.token) {
        state.token = localStorage.getItem('token')
      }
      return state.token
    },
    getUserInfo(state) {
      return state.userInfo
    },
    // 新的权限相关getters
    permissions(state) {
      if (!state.permissions || state.permissions.length === 0) {
        const stored = localStorage.getItem('permissions')
        if (stored) {
          state.permissions = JSON.parse(stored)
        }
      }
      return state.permissions
    },
    roles(state) {
      if (!state.roles || state.roles.length === 0) {
        const stored = localStorage.getItem('roles')
        if (stored) {
          state.roles = JSON.parse(stored)
        }
      }
      return state.roles
    },
    userType(state) {
      // 基于权限判断用户类型
      const permissions = state.permissions || []
      if (permissions.includes('*:*:*') || permissions.includes('organization:view:*')) {
        return 1 // 平台管理员
      } else if (permissions.some(p => p.includes(':tenant'))) {
        return 2 // 租户管理员
      } else {
        return 3 // 普通用户
      }
    },
    // 保留兼容性
    getIsSuperAdmin(state, getters) {
      return getters.userType === 1
    },
    getPubConfig(state) {
      return state.pubConfig
    }
  },
  mutations: {
    setToken(state, token) {
      state.token = token
      localStorage.setItem('token', token)
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo
      // 保留兼容性
      const isSuperAdmin = userInfo.superAdmin === 1
      state.isSuperAdmin = isSuperAdmin
      localStorage.setItem('isSuperAdmin', isSuperAdmin)
    },
    setPermissions(state, permissions) {
      state.permissions = permissions || []
      localStorage.setItem('permissions', JSON.stringify(permissions || []))
    },
    setRoles(state, roles) {
      state.roles = roles || []
      localStorage.setItem('roles', JSON.stringify(roles || []))
    },
    setPubConfig(state, config) {
      state.pubConfig = config
    },
    clearAuth(state) {
      state.token = ''
      state.userInfo = {}
      state.permissions = []
      state.roles = []
      state.isSuperAdmin = false
      localStorage.removeItem('token')
      localStorage.removeItem('permissions')
      localStorage.removeItem('roles')
      localStorage.removeItem('isSuperAdmin')
    }
  },
  actions: {
    // 登录并获取用户权限
    login({ commit }, loginData) {
      return new Promise((resolve, reject) => {
        Api.user.login(loginData, ({ data }) => {
          if (data.code === 0) {
            commit('setToken', data.data.token)
            commit('setUserInfo', data.data.userInfo)
            // 登录成功后获取权限
            this.dispatch('fetchUserPermissions')
            resolve(data)
          } else {
            reject(data)
          }
        }, (error) => {
          reject(error)
        })
      })
    },

    // 获取用户权限
    fetchUserPermissions({ commit }) {
      return new Promise((resolve) => {
        permissionApi.getCurrentUserPermissions((res) => {
          if (res.code === 0) {
            commit('setPermissions', res.data.permissions || [])
            commit('setRoles', res.data.roles || [])
          }
          resolve()
        })
      })
    },

    // 退出登录
    logout({ commit }) {
      return new Promise((resolve) => {
        commit('clearAuth')
        goToPage(Constant.PAGE.LOGIN, true);
        window.location.reload(); // 彻底重置状态
        resolve()
      })
    },

    // 获取公共配置
    fetchPubConfig({ commit }) {
      return new Promise((resolve) => {
        Api.user.getPubConfig(({ data }) => {
          if (data.code === 0) {
            commit('setPubConfig', data.data);
          }
          resolve();
        });
      });
    }
  },
  modules: {
  }
})