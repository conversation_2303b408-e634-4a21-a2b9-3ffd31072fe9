package xiaozhi.modules.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 企业更新请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "企业更新请求")
public class OrganizationUpdateRequestDTO {
    
    @Schema(description = "企业ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "企业ID不能为空")
    private Long id;
    
    @Schema(description = "企业编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORG001")
    @NotBlank(message = "企业编码不能为空")
    @Pattern(regexp = "^[A-Z0-9]{3,20}$", message = "企业编码格式不正确，只能包含大写字母和数字，长度3-20位")
    private String orgCode;
    
    @Schema(description = "企业名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试企业")
    @NotBlank(message = "企业名称不能为空")
    @Size(min = 2, max = 100, message = "企业名称长度为2-100位")
    private String orgName;
    
    @Schema(description = "企业类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1", allowableValues = {"1", "2"})
    @NotNull(message = "企业类型不能为空")
    private Integer orgType;
    
    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotBlank(message = "联系人不能为空")
    @Size(min = 2, max = 50, message = "联系人姓名长度为2-50位")
    private String contactPerson;
    
    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    private String contactPhone;
    
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String contactEmail;
    
    @Schema(description = "企业地址", example = "北京市朝阳区")
    @Size(max = 200, message = "企业地址长度不能超过200字符")
    private String address;
    
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;
    
    @Schema(description = "到期时间", example = "2025-12-31 23:59:59")
    private Date expireDate;
    
    @Schema(description = "备注", example = "测试企业备注")
    @Size(max = 500, message = "备注长度不能超过500字符")
    private String remark;
}
