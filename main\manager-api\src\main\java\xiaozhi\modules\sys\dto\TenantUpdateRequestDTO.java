package xiaozhi.modules.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import java.util.Date;

/**
 * 租户更新请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "租户更新请求")
public class TenantUpdateRequestDTO {
    
    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租户ID不能为空")
    private Long id;
    
    @Schema(description = "租户编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "TENANT001")
    @NotBlank(message = "租户编码不能为空")
    @Pattern(regexp = "^[A-Z0-9]{6,20}$", message = "租户编码格式不正确，只能包含大写字母和数字，长度6-20位")
    private String tenantCode;
    
    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试租户")
    @NotBlank(message = "租户名称不能为空")
    @Size(min = 2, max = 50, message = "租户名称长度为2-50位")
    private String tenantName;
    
    @Schema(description = "企业ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "企业ID不能为空")
    private Long orgId;
    
    @Schema(description = "租户管理员用户ID", example = "1")
    private Long adminUserId;
    
    @Schema(description = "最大用户数", example = "100")
    @Min(value = 1, message = "最大用户数不能小于1")
    private Integer maxUsers;
    
    @Schema(description = "最大设备数", example = "1000")
    @Min(value = 1, message = "最大设备数不能小于1")
    private Integer maxDevices;
    
    @Schema(description = "最大智能体数", example = "50")
    @Min(value = 1, message = "最大智能体数不能小于1")
    private Integer maxAgents;
    
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;
    
    @Schema(description = "到期时间", example = "2025-12-31 23:59:59")
    private Date expireDate;
    
    @Schema(description = "备注", example = "测试租户备注")
    @Size(max = 500, message = "备注长度不能超过500字符")
    private String remark;
}
