package xiaozhi.modules.sys.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import xiaozhi.common.page.PageData;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.tenant.TenantContext;
import xiaozhi.modules.sys.dto.UserCreateRequestDTO;
import xiaozhi.modules.sys.dto.UserDetailResponseDTO;
import xiaozhi.modules.sys.dto.UserQueryRequestDTO;
import xiaozhi.modules.sys.dto.UserUpdateRequestDTO;
import xiaozhi.modules.sys.service.SysUserService;

import java.util.List;

/**
 * 用户管理控制层
 *
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@RestController
@RequestMapping("/sys/user")
@Tag(name = "用户管理", description = "统一的用户管理功能，支持多租户数据隔离")
public class UserController {
    private final SysUserService sysUserService;
    @PostMapping("/page")
    @Operation(
            summary = "分页查询用户",
            description = "分页查询用户列表，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "user:view:*", description = "查看用户权限")
    public Result<PageData<UserDetailResponseDTO>> page(@Parameter(description = "查询参数")
            @RequestBody UserQueryRequestDTO queryRequest) {
        // 参数验证
        ValidatorUtils.validateEntity(queryRequest);

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        PageData<UserDetailResponseDTO> pageData = sysUserService.pageWithDetails(queryRequest);
        return new Result<PageData<UserDetailResponseDTO>>().ok(pageData);
    }

    @GetMapping("/list")
    @Operation(
            operationId = "user list",
            summary = "获取用户列表",
            description = "获取用户列表，不分页，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "user:view:*", description = "查看用户权限")
    public Result<List<UserDetailResponseDTO>> list() {
        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        List<UserDetailResponseDTO> userList = sysUserService.getAllUsers();
        return new Result<List<UserDetailResponseDTO>>().ok(userList);
    }

    @GetMapping("/{userId}")
    @Operation(
            operationId = "user info",
            summary = "获取用户详情",
            description = "根据用户ID获取用户详细信息"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:view:*", description = "查看用户权限")
    public Result<UserDetailResponseDTO> info(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable(name = "userId") Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        // 如果用户不在当前租户，拦截器会自动过滤，返回null
        UserDetailResponseDTO user = sysUserService.getUserDetail(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在或无权限访问");
        }

        return new Result<UserDetailResponseDTO>().ok(user);
    }

    @PostMapping
    @Operation(
        summary = "创建用户",
        description = "创建新用户，自动设置租户信息"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数验证失败")
    })
    @RequiresPermission(value = "user:create:*", description = "创建用户权限")
    public Result<Void> create(@RequestBody UserCreateRequestDTO createRequest) {
        // 参数验证
        ValidatorUtils.validateEntity(createRequest);

        // 权限检查：非平台管理员不能创建平台管理员
        if (!TenantContext.isPlatformAdmin() && createRequest.getUserType() == 1) {
            throw new RuntimeException("无权限创建平台管理员");
        }

        sysUserService.createUser(createRequest);

        return new Result<>();
    }

    @PutMapping
    @Operation(
        summary = "更新用户",
        description = "更新用户信息，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:update:*", description = "编辑用户权限")
    public Result<Void> update(@RequestBody UserUpdateRequestDTO updateRequest) {
        // 参数验证
        ValidatorUtils.validateEntity(updateRequest);

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        // 如果用户不在当前租户，更新操作会被拦截器阻止
        sysUserService.updateUser(updateRequest);

        return new Result<>();
    }

    @DeleteMapping("/{userId}")
    @Operation(
        summary = "删除用户",
        description = "删除指定用户，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:delete:*", description = "删除用户权限")
    public Result<Void> delete(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable(name = "userId") Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        // 如果用户不在当前租户，删除操作会被拦截器阻止
        sysUserService.deleteUser(userId);

        return new Result<>();
    }

    @PutMapping("/{userId}/status")
    @Operation(
        summary = "修改用户状态",
        description = "启用或禁用用户，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "修改成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:update:*", description = "编辑用户权限")
    public Result<Void> updateStatus(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId,
            @Parameter(description = "状态", required = true, example = "1")
            @RequestParam Integer status) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        sysUserService.updateUserStatus(userId, status);

        return new Result<>();
    }

    @PutMapping("/{userId}/reset-password")
    @Operation(
        summary = "重置用户密码",
        description = "重置用户密码为默认密码，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "重置成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:update:*", description = "编辑用户权限")
    public Result<String> resetPassword(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        String newPassword = sysUserService.resetPassword(userId);

        return new Result<String>().ok(newPassword);
    }

}
