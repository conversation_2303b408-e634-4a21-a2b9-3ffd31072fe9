package xiaozhi.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 智能体统计响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "智能体统计响应")
public class AgentStatisticsResponseDTO {
    
    @Schema(description = "智能体ID", example = "agent123")
    private String agentId;
    
    @Schema(description = "智能体名称", example = "客服助手")
    private String agentName;
    
    @Schema(description = "总使用次数", example = "1000")
    private Integer totalUsageCount;
    
    @Schema(description = "总使用时长（秒）", example = "36000")
    private Long totalDuration;
    
    @Schema(description = "平均使用时长（秒）", example = "36")
    private Long avgDuration;
    
    @Schema(description = "使用用户数", example = "50")
    private Integer userCount;
    
    @Schema(description = "成功率", example = "0.95")
    private Double successRate;
    
    @Schema(description = "错误率", example = "0.05")
    private Double errorRate;
    
    @Schema(description = "统计开始时间")
    private Date startDate;
    
    @Schema(description = "统计结束时间")
    private Date endDate;
    
    @Schema(description = "每日统计详情")
    private List<DailyStatistics> dailyStatistics;
    
    @Data
    @Schema(description = "每日统计")
    public static class DailyStatistics {
        
        @Schema(description = "统计日期")
        private Date date;
        
        @Schema(description = "使用次数")
        private Integer usageCount;
        
        @Schema(description = "使用时长（秒）")
        private Long duration;
        
        @Schema(description = "使用用户数")
        private Integer userCount;
        
        @Schema(description = "成功次数")
        private Integer successCount;
        
        @Schema(description = "失败次数")
        private Integer failureCount;
    }
}
