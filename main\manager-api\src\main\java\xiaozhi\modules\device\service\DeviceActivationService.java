package xiaozhi.modules.device.service;

import java.util.List;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.device.dto.DeviceActivationQueryDTO;
import xiaozhi.modules.device.entity.DeviceActivationEntity;

/**
 * 设备激活服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface DeviceActivationService extends BaseService<DeviceActivationEntity> {
    
    /**
     * 分页查询设备激活记录
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    PageData<DeviceActivationEntity> pageActivations(DeviceActivationQueryDTO queryDTO);
    
    /**
     * 创建设备激活记录
     *
     * @param macAddress MAC地址
     * @param agentId 智能体ID
     * @param activationCode 激活码
     * @return 激活记录
     */
    DeviceActivationEntity createActivationRecord(String macAddress, String agentId, String activationCode);
    
    /**
     * 更新设备激活状态
     *
     * @param activationCode 激活码
     * @param status 激活状态
     * @param deviceId 设备ID
     * @param failureReason 失败原因
     */
    void updateActivationStatus(String activationCode, Integer status, String deviceId, String failureReason);
    
    /**
     * 根据激活码获取激活记录
     *
     * @param activationCode 激活码
     * @return 激活记录
     */
    DeviceActivationEntity getByActivationCode(String activationCode);
    
    /**
     * 根据MAC地址获取激活记录
     *
     * @param macAddress MAC地址
     * @return 激活记录列表
     */
    List<DeviceActivationEntity> getByMacAddress(String macAddress);
    
    /**
     * 根据设备ID获取激活记录
     *
     * @param deviceId 设备ID
     * @return 激活记录列表
     */
    List<DeviceActivationEntity> getByDeviceId(String deviceId);
    
    /**
     * 检查激活码是否有效
     *
     * @param activationCode 激活码
     * @return 是否有效
     */
    boolean isActivationCodeValid(String activationCode);
    
    /**
     * 获取用户未激活的设备数量
     *
     * @param userId 用户ID
     * @return 未激活设备数量
     */
    int getUnactivatedDeviceCount(Long userId);
    
    /**
     * 获取租户激活统计
     *
     * @param tenantId 租户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 激活统计
     */
    List<Object> getTenantActivationStatistics(Long tenantId, String startDate, String endDate);
}
