package xiaozhi.modules.agent.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import xiaozhi.common.dao.BaseDao;
import xiaozhi.modules.agent.entity.AgentAssignmentEntity;

/**
 * 智能体分配DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Mapper
public interface AgentAssignmentDao extends BaseDao<AgentAssignmentEntity> {
    
    /**
     * 根据智能体ID获取分配记录
     * 
     * @param agentId 智能体ID
     * @return 分配记录列表
     */
    List<AgentAssignmentEntity> getAssignmentsByAgentId(@Param("agentId") String agentId);
    
    /**
     * 根据用户ID获取分配记录
     * 
     * @param userId 用户ID
     * @return 分配记录列表
     */
    List<AgentAssignmentEntity> getAssignmentsByUserId(@Param("userId") Long userId);
    
    /**
     * 检查智能体是否已分配给用户
     * 
     * @param agentId 智能体ID
     * @param userId 用户ID
     * @return 是否已分配
     */
    boolean isAgentAssignedToUser(@Param("agentId") String agentId, @Param("userId") Long userId);
    
    /**
     * 获取用户已分配的智能体ID列表
     * 
     * @param userId 用户ID
     * @return 智能体ID列表
     */
    List<String> getAssignedAgentIdsByUserId(@Param("userId") Long userId);
    
    /**
     * 获取智能体已分配的用户ID列表
     * 
     * @param agentId 智能体ID
     * @return 用户ID列表
     */
    List<Long> getAssignedUserIdsByAgentId(@Param("agentId") String agentId);
    
    /**
     * 批量分配智能体
     * 
     * @param assignments 分配记录列表
     * @return 影响行数
     */
    int batchInsertAssignments(@Param("assignments") List<AgentAssignmentEntity> assignments);
    
    /**
     * 批量取消分配
     * 
     * @param agentIds 智能体ID列表
     * @param userIds 用户ID列表
     * @param unassignedBy 取消分配者ID
     * @return 影响行数
     */
    int batchUnassignAgents(@Param("agentIds") List<String> agentIds, 
                           @Param("userIds") List<Long> userIds, 
                           @Param("unassignedBy") Long unassignedBy);
}
