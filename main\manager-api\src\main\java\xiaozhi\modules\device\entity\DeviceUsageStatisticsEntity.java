package xiaozhi.modules.device.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备使用统计实体
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@TableName("ai_device_usage_statistics")
@Schema(description = "设备使用统计信息")
public class DeviceUsageStatisticsEntity {

    @TableId(type = IdType.AUTO)
    @Schema(description = "统计ID")
    private Long id;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备MAC地址")
    private String macAddress;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "统计日期")
    private Date statisticsDate;

    @Schema(description = "连接次数")
    private Integer connectionCount;

    @Schema(description = "总在线时长（秒）")
    private Long totalOnlineDuration;

    @Schema(description = "平均在线时长（秒）")
    private Long avgOnlineDuration;

    @Schema(description = "最长在线时长（秒）")
    private Long maxOnlineDuration;

    @Schema(description = "最短在线时长（秒）")
    private Long minOnlineDuration;

    @Schema(description = "成功连接次数")
    private Integer successConnectionCount;

    @Schema(description = "失败连接次数")
    private Integer failureConnectionCount;

    @Schema(description = "连接成功率")
    private Double connectionSuccessRate;

    @Schema(description = "数据传输量（字节）")
    private Long dataTransferBytes;

    @Schema(description = "最后连接时间")
    private Date lastConnectedAt;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;
}
