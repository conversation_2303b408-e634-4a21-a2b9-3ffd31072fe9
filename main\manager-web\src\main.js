import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import 'normalize.css/normalize.css'; // A modern alternative to CSS resets
import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import './styles/global.scss';
import { register as registerServiceWorker } from './registerServiceWorker';

// 导入权限指令
import permission, { permissionDisabled } from '@/directive/permission'
// 导入权限初始化
import { initAuth, setupRouterGuards } from '@/utils/auth'

Vue.use(ElementUI);

// 注册权限指令
Vue.directive('permission', permission)
Vue.directive('permission-disabled', permissionDisabled)

// 设置路由守卫
setupRouterGuards()

Vue.config.productionTip = false

// 注册Service Worker
registerServiceWorker();

// 创建Vue实例
const app = new Vue({
  router,
  store,
  render: function (h) { return h(App) }
})

// 初始化权限系统
initAuth().finally(() => {
  app.$mount('#app')
})
