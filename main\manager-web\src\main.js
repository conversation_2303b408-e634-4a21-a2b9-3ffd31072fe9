import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import 'normalize.css/normalize.css'; // A modern alternative to CSS resets
import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import './styles/global.scss';
import { register as registerServiceWorker } from './registerServiceWorker';

Vue.use(ElementUI);

// 创建一个简单的权限指令，避免复杂的权限系统
Vue.directive('permission', {
  inserted(el, binding) {
    // 暂时显示所有元素，避免权限系统导致的问题
    // 后续可以根据实际需要启用权限控制
  },
  update(el, binding) {
    // 暂时不做任何处理
  }
})

Vue.directive('permission-disabled', {
  inserted(el, binding) {
    // 暂时不禁用任何元素
  },
  update(el, binding) {
    // 暂时不做任何处理
  }
})

// 创建一个简单的权限混入
Vue.mixin({
  data() {
    return {
      PERMISSIONS: {} // 空的权限对象
    }
  },
  computed: {
    userType() {
      return this.$store.getters.userType || 1
    },
    userPermissions() {
      return this.$store.getters.permissions || []
    },
    userRoles() {
      return this.$store.getters.roles || []
    },
    currentUser() {
      return this.$store.getters.getUserInfo || {}
    }
  },
  methods: {
    // 提供基础的权限检查方法，暂时都返回true
    hasPermission() { return true },
    hasAnyPermission() { return true },
    hasAllPermissions() { return true },
    isPlatformAdmin() { return true },
    isTenantAdmin() { return true },
    isNormalUser() { return false },
    canViewOrganization() { return true },
    canCreateOrganization() { return true },
    canUpdateOrganization() { return true },
    canDeleteOrganization() { return true },
    canViewTenant() { return true },
    canCreateTenant() { return true },
    canUpdateTenant() { return true },
    canDeleteTenant() { return true },
    canViewUser() { return true },
    canCreateUser() { return true },
    canUpdateUser() { return true },
    canDeleteUser() { return true },
    canResetPassword() { return true },
    canChangeUserStatus() { return true },
    handlePermissionError() {
      this.$message.error('您没有权限执行此操作')
    }
  }
})

// 创建Vue实例
new Vue({
  router,
  store,
  render: function (h) { return h(App) }
}).$mount('#app')

Vue.config.productionTip = false

// 注册Service Worker
registerServiceWorker();


