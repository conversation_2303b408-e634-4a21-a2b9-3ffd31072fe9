package xiaozhi.modules.sys.service;

import java.util.List;
import java.util.Set;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.sys.entity.SysRoleEntity;

/**
 * 角色Service
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface SysRoleService extends BaseService<SysRoleEntity> {
    
    /**
     * 根据用户ID获取角色列表
     * 
     * @param userId 用户ID
     * @return 角色编码集合
     */
    Set<String> getUserRoles(Long userId);
    
    /**
     * 根据租户ID获取角色列表
     * 
     * @param tenantId 租户ID
     * @return 角色实体列表
     */
    List<SysRoleEntity> getRolesByTenantId(Long tenantId);
    
    /**
     * 根据角色类型获取角色列表
     * 
     * @param roleType 角色类型
     * @return 角色实体列表
     */
    List<SysRoleEntity> getRolesByType(Integer roleType);
}
