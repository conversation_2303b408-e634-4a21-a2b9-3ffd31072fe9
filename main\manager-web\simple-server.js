const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const port = 8001;

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm'
};

const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  
  // 解析URL
  const parsedUrl = url.parse(req.url);
  let pathname = parsedUrl.pathname;
  
  // 如果是根路径，重定向到test.html
  if (pathname === '/') {
    pathname = '/test.html';
  }
  
  // 构建文件路径
  let filePath = path.join(__dirname, pathname);
  
  // 检查文件是否存在
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // 文件不存在，返回404
      res.writeHead(404, { 'Content-Type': 'text/html' });
      res.end(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>404 - 页面未找到</title>
            <meta charset="UTF-8">
        </head>
        <body>
            <h1>404 - 页面未找到</h1>
            <p>请求的文件不存在: ${pathname}</p>
            <p><a href="/test.html">返回测试页面</a></p>
            <p><a href="/header-test.html">HeaderBar测试页面</a></p>
        </body>
        </html>
      `);
      return;
    }
    
    // 获取文件扩展名
    const ext = path.parse(filePath).ext;
    const mimeType = mimeTypes[ext] || 'text/plain';
    
    // 读取文件
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/html' });
        res.end(`
          <!DOCTYPE html>
          <html>
          <head>
              <title>500 - 服务器错误</title>
              <meta charset="UTF-8">
          </head>
          <body>
              <h1>500 - 服务器错误</h1>
              <p>读取文件时发生错误: ${err.message}</p>
          </body>
          </html>
        `);
        return;
      }
      
      // 设置响应头
      res.writeHead(200, { 
        'Content-Type': mimeType,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      });
      res.end(data);
    });
  });
});

server.listen(port, () => {
  console.log(`简单HTTP服务器已启动`);
  console.log(`访问地址: http://localhost:${port}`);
  console.log(`测试页面: http://localhost:${port}/test.html`);
  console.log(`HeaderBar测试: http://localhost:${port}/header-test.html`);
  console.log(`按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
