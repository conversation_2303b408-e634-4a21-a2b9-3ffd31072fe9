package xiaozhi.modules.agent.service;

import java.util.List;
import java.util.Map;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.agent.dto.AgentStatisticsQueryDTO;
import xiaozhi.modules.agent.dto.AgentStatisticsResponseDTO;
import xiaozhi.modules.agent.entity.AgentUsageStatisticsEntity;

/**
 * 智能体统计服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface AgentStatisticsService extends BaseService<AgentUsageStatisticsEntity> {
    
    /**
     * 获取智能体使用统计
     *
     * @param queryDTO 查询条件
     * @return 统计数据
     */
    AgentStatisticsResponseDTO getAgentStatistics(AgentStatisticsQueryDTO queryDTO);
    
    /**
     * 获取热门智能体排行
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行列表
     */
    List<Map<String, Object>> getPopularAgentsRanking(String startDate, String endDate, Integer limit);
    
    /**
     * 记录智能体使用情况
     *
     * @param agentId 智能体ID
     * @param userId 用户ID
     * @param duration 使用时长
     * @param isSuccess 是否成功
     */
    void recordAgentUsage(String agentId, Long userId, Long duration, Boolean isSuccess);
    
    /**
     * 获取租户智能体统计概览
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    Map<String, Object> getTenantAgentOverview(Long tenantId);
    
    /**
     * 获取智能体总体统计信息
     *
     * @param agentId 智能体ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Map<String, Object> getAgentOverallStatistics(String agentId, String startDate, String endDate);
    
    /**
     * 获取用户智能体使用统计
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<AgentUsageStatisticsEntity> getUserAgentStatistics(Long userId, String startDate, String endDate);
}
