<template>
  <div class="permission-debug">
    <HeaderBar />
    
    <div class="main-content">
      <div class="content-header">
        <h2>权限调试工具</h2>
        <el-alert
          title="仅在开发环境使用"
          type="warning"
          description="此工具仅用于开发调试，生产环境请勿使用"
          show-icon
          :closable="false">
        </el-alert>
      </div>

      <el-tabs v-model="activeTab">
        <!-- 当前权限信息 -->
        <el-tab-pane label="当前权限" name="current">
          <div class="debug-section">
            <h3>用户信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户名">{{ currentUser.username }}</el-descriptions-item>
              <el-descriptions-item label="真实姓名">{{ currentUser.realName }}</el-descriptions-item>
              <el-descriptions-item label="用户类型">{{ userTypeText }}</el-descriptions-item>
              <el-descriptions-item label="手机号">{{ currentUser.mobile }}</el-descriptions-item>
            </el-descriptions>

            <h3>角色列表</h3>
            <el-tag v-for="role in userRoles" :key="role.id" style="margin: 5px;">
              {{ role.roleName }}
            </el-tag>
            <span v-if="userRoles.length === 0" class="no-data">暂无角色</span>

            <h3>权限列表</h3>
            <div class="permission-list">
              <el-tag 
                v-for="permission in userPermissions" 
                :key="permission"
                type="success"
                style="margin: 5px;">
                {{ permission }}
              </el-tag>
              <span v-if="userPermissions.length === 0" class="no-data">暂无权限</span>
            </div>
          </div>
        </el-tab-pane>

        <!-- 权限检查 -->
        <el-tab-pane label="权限检查" name="check">
          <div class="debug-section">
            <h3>权限检查工具</h3>
            <el-form :inline="true">
              <el-form-item label="权限代码">
                <el-input 
                  v-model="checkPermission" 
                  placeholder="请输入权限代码，如：user:view:*"
                  style="width: 300px;">
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleCheckPermission">检查权限</el-button>
              </el-form-item>
            </el-form>

            <div v-if="checkResult !== null" class="check-result">
              <el-alert
                :title="checkResult ? '✅ 有权限' : '❌ 无权限'"
                :type="checkResult ? 'success' : 'error'"
                :description="`权限检查结果：${checkPermission}`"
                show-icon
                :closable="false">
              </el-alert>
            </div>

            <h3>常用权限快速检查</h3>
            <div class="quick-check">
              <el-button 
                v-for="(permission, key) in commonPermissions" 
                :key="key"
                size="mini"
                :type="hasPermission(permission.code) ? 'success' : 'danger'"
                @click="quickCheck(permission.code)">
                {{ permission.name }}
              </el-button>
            </div>
          </div>
        </el-tab-pane>

        <!-- 权限模拟 -->
        <el-tab-pane label="权限模拟" name="mock">
          <div class="debug-section">
            <h3>权限模拟工具</h3>
            <el-alert
              title="注意"
              type="warning"
              description="权限模拟仅在前端生效，不会影响后端权限验证"
              show-icon
              :closable="false">
            </el-alert>

            <el-form style="margin-top: 20px;">
              <el-form-item label="选择角色模板">
                <el-select v-model="selectedRole" placeholder="请选择角色" @change="handleRoleChange">
                  <el-option label="平台管理员" value="PLATFORM_ADMIN" />
                  <el-option label="租户管理员" value="TENANT_ADMIN" />
                  <el-option label="普通用户" value="NORMAL_USER" />
                  <el-option label="自定义" value="CUSTOM" />
                </el-select>
              </el-form-item>

              <el-form-item label="权限列表">
                <el-input
                  v-model="mockPermissionsText"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入权限列表，每行一个权限代码">
                </el-input>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="applyMockPermissions">应用模拟权限</el-button>
                <el-button @click="resetPermissions">重置权限</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 系统权限 -->
        <el-tab-pane label="系统权限" name="system">
          <div class="debug-section">
            <h3>系统权限定义</h3>
            <el-table :data="allPermissions" border stripe>
              <el-table-column prop="code" label="权限代码" min-width="200" />
              <el-table-column prop="name" label="权限名称" width="150" />
              <el-table-column prop="description" label="权限描述" min-width="200" />
              <el-table-column label="当前状态" width="100">
                <template slot-scope="scope">
                  <el-tag :type="hasPermission(scope.row.code) ? 'success' : 'info'">
                    {{ hasPermission(scope.row.code) ? '有权限' : '无权限' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <VersionFooter />
  </div>
</template>

<script>
import HeaderBar from "@/components/HeaderBar.vue";
import VersionFooter from "@/components/VersionFooter.vue";
import { getAllPermissions, ROLE_PERMISSIONS } from "@/config/permissions";

export default {
  name: "PermissionDebug",
  components: {
    HeaderBar,
    VersionFooter
  },
  data() {
    return {
      activeTab: 'current',
      checkPermission: '',
      checkResult: null,
      selectedRole: '',
      mockPermissionsText: '',
      allPermissions: getAllPermissions(),
      commonPermissions: {
        viewOrg: { code: 'organization:view:*', name: '查看企业' },
        createOrg: { code: 'organization:create:*', name: '创建企业' },
        viewTenant: { code: 'tenant:view:*', name: '查看租户' },
        createTenant: { code: 'tenant:create:*', name: '创建租户' },
        viewUser: { code: 'user:view:*', name: '查看用户' },
        createUser: { code: 'user:create:*', name: '创建用户' },
        viewPermission: { code: 'permission:view:*', name: '查看权限' },
        assignPermission: { code: 'permission:assign:*', name: '分配权限' }
      }
    };
  },
  methods: {
    // 检查权限
    handleCheckPermission() {
      if (!this.checkPermission.trim()) {
        this.$message.warning('请输入权限代码');
        return;
      }
      this.checkResult = this.hasPermission(this.checkPermission.trim());
    },

    // 快速检查
    quickCheck(permission) {
      this.checkPermission = permission;
      this.checkResult = this.hasPermission(permission);
    },

    // 角色变更
    handleRoleChange(role) {
      if (role && ROLE_PERMISSIONS[role]) {
        this.mockPermissionsText = ROLE_PERMISSIONS[role].join('\n');
      } else if (role === 'CUSTOM') {
        this.mockPermissionsText = '';
      }
    },

    // 应用模拟权限
    applyMockPermissions() {
      const permissions = this.mockPermissionsText
        .split('\n')
        .map(p => p.trim())
        .filter(p => p);
      
      this.$store.commit('setPermissions', permissions);
      this.$message.success('模拟权限已应用');
      
      // 刷新当前标签页
      if (this.activeTab === 'current') {
        this.$forceUpdate();
      }
    },

    // 重置权限
    resetPermissions() {
      this.$store.dispatch('fetchUserPermissions').then(() => {
        this.$message.success('权限已重置');
        this.mockPermissionsText = '';
        this.selectedRole = '';
        
        // 刷新当前标签页
        if (this.activeTab === 'current') {
          this.$forceUpdate();
        }
      });
    }
  }
};
</script>

<style scoped>
.permission-debug {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.content-header {
  margin-bottom: 20px;
}

.content-header h2 {
  margin: 0 0 15px 0;
  color: #333;
}

.debug-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.debug-section h3 {
  margin: 20px 0 15px 0;
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.debug-section h3:first-child {
  margin-top: 0;
}

.permission-list {
  min-height: 40px;
}

.no-data {
  color: #999;
  font-style: italic;
}

.check-result {
  margin: 15px 0;
}

.quick-check {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.quick-check .el-button {
  margin: 0;
}
</style>
