import store from '@/store'
import router from '@/router'
import { Message } from 'element-ui'

/**
 * 权限初始化
 * 在应用启动时调用，检查用户登录状态和权限
 */
export function initAuth() {
  const token = store.getters.getToken
  
  if (token) {
    // 如果有token，获取用户信息和权限
    return Promise.all([
      store.dispatch('fetchUserPermissions'),
      store.dispatch('fetchPubConfig')
    ]).catch(error => {
      console.error('权限初始化失败:', error)
      // 如果获取权限失败，清除认证信息
      store.commit('clearAuth')
      router.push('/login')
    })
  } else {
    // 没有token，跳转到登录页
    if (router.currentRoute.path !== '/login') {
      router.push('/login')
    }
    return Promise.resolve()
  }
}

/**
 * 登录处理
 * @param {Object} loginData 登录数据
 * @returns {Promise}
 */
export function handleLogin(loginData) {
  return store.dispatch('login', loginData)
    .then(() => {
      // 登录成功后获取权限
      return store.dispatch('fetchUserPermissions')
    })
    .then(() => {
      Message.success('登录成功')
      router.push('/home')
    })
    .catch(error => {
      console.error('登录失败:', error)
      Message.error(error.msg || '登录失败')
      throw error
    })
}

/**
 * 退出登录处理
 */
export function handleLogout() {
  return store.dispatch('logout')
    .then(() => {
      Message.success('退出登录成功')
    })
    .catch(error => {
      console.error('退出登录失败:', error)
      Message.error('退出登录失败')
    })
}

/**
 * 检查路由权限
 * @param {Object} to 目标路由
 * @param {Object} from 来源路由
 * @param {Function} next 下一步函数
 */
export function checkRoutePermission(to, from, next) {
  const token = store.getters.getToken
  
  // 如果没有token且不是登录页，跳转到登录页
  if (!token && to.path !== '/login') {
    next('/login')
    return
  }
  
  // 如果有token但是访问登录页，跳转到首页
  if (token && to.path === '/login') {
    next('/home')
    return
  }
  
  // 检查路由权限
  if (to.meta && to.meta.permission) {
    const hasPermission = store.getters.permissions.includes(to.meta.permission) ||
                         store.getters.permissions.includes('*:*:*')
    
    if (!hasPermission) {
      Message.error('您没有权限访问该页面')
      next(false)
      return
    }
  }
  
  next()
}

/**
 * 权限路由守卫
 * 在路由跳转前检查权限
 */
export function setupRouterGuards() {
  router.beforeEach((to, from, next) => {
    checkRoutePermission(to, from, next)
  })
}

/**
 * 获取用户类型文本
 * @param {Number} userType 用户类型
 * @returns {String}
 */
export function getUserTypeText(userType) {
  const typeMap = {
    1: '平台管理员',
    2: '租户管理员',
    3: '普通用户'
  }
  return typeMap[userType] || '未知'
}

/**
 * 获取用户状态文本
 * @param {Number} status 用户状态
 * @returns {String}
 */
export function getUserStatusText(status) {
  const statusMap = {
    0: '禁用',
    1: '正常'
  }
  return statusMap[status] || '未知'
}

/**
 * 权限错误处理
 * @param {Error} error 错误对象
 */
export function handlePermissionError(error) {
  console.error('权限错误:', error)
  
  if (error.response) {
    const { status, data } = error.response
    
    switch (status) {
      case 401:
        Message.error('登录已过期，请重新登录')
        store.commit('clearAuth')
        router.push('/login')
        break
      case 403:
        Message.error('您没有权限执行此操作')
        break
      case 404:
        Message.error('请求的资源不存在')
        break
      case 500:
        Message.error('服务器内部错误')
        break
      default:
        Message.error(data.msg || '操作失败')
    }
  } else {
    Message.error('网络错误，请检查网络连接')
  }
}

/**
 * 权限缓存管理
 */
export const PermissionCache = {
  // 缓存权限数据
  cachePermissions(permissions, roles) {
    localStorage.setItem('permissions', JSON.stringify(permissions || []))
    localStorage.setItem('roles', JSON.stringify(roles || []))
    localStorage.setItem('permissionCacheTime', Date.now().toString())
  },
  
  // 获取缓存的权限数据
  getCachedPermissions() {
    const permissions = localStorage.getItem('permissions')
    const roles = localStorage.getItem('roles')
    const cacheTime = localStorage.getItem('permissionCacheTime')
    
    // 检查缓存是否过期（24小时）
    if (cacheTime && Date.now() - parseInt(cacheTime) > 24 * 60 * 60 * 1000) {
      this.clearPermissionCache()
      return null
    }
    
    return {
      permissions: permissions ? JSON.parse(permissions) : [],
      roles: roles ? JSON.parse(roles) : []
    }
  },
  
  // 清除权限缓存
  clearPermissionCache() {
    localStorage.removeItem('permissions')
    localStorage.removeItem('roles')
    localStorage.removeItem('permissionCacheTime')
  }
}

/**
 * 权限调试工具
 */
export const PermissionDebug = {
  // 打印当前用户权限
  logCurrentPermissions() {
    const permissions = store.getters.permissions
    const roles = store.getters.roles
    const userType = store.getters.userType
    
    console.group('当前用户权限信息')
    console.log('用户类型:', getUserTypeText(userType))
    console.log('角色列表:', roles)
    console.log('权限列表:', permissions)
    console.groupEnd()
  },
  
  // 检查特定权限
  checkPermission(permission) {
    const hasPermission = store.getters.permissions.includes(permission) ||
                         store.getters.permissions.includes('*:*:*')
    console.log(`权限检查 [${permission}]:`, hasPermission ? '✅ 有权限' : '❌ 无权限')
    return hasPermission
  },
  
  // 模拟权限
  mockPermissions(permissions) {
    console.warn('⚠️ 正在模拟权限，仅用于开发调试')
    store.commit('setPermissions', permissions)
  }
}
