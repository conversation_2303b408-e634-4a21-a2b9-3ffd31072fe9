package xiaozhi.modules.sys.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;

import lombok.AllArgsConstructor;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.sys.dao.SysUserRoleDao;
import xiaozhi.modules.sys.entity.SysUserRoleEntity;
import xiaozhi.modules.sys.service.SysUserRoleService;

/**
 * 用户角色关联Service实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class SysUserRoleServiceImpl extends BaseServiceImpl<SysUserRoleDao, SysUserRoleEntity> implements SysUserRoleService {
    
    private final SysUserRoleDao sysUserRoleDao;
    
    @Override
    public List<Long> getRoleIdsByUserId(Long userId) {
        return sysUserRoleDao.getRoleIdsByUserId(userId);
    }
    
    @Override
    public List<Long> getUserIdsByRoleId(Long roleId) {
        QueryWrapper<SysUserRoleEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("role_id", roleId);
        wrapper.eq("deleted", 0);
        wrapper.select("user_id");
        
        return list(wrapper).stream()
            .map(SysUserRoleEntity::getUserId)
            .toList();
    }
    
    @Override
    public int deleteByUserId(Long userId) {
        return sysUserRoleDao.deleteByUserId(userId);
    }
    
    @Override
    public int deleteByRoleId(Long roleId) {
        return sysUserRoleDao.deleteByRoleId(roleId);
    }
    
    @Override
    public int deleteByUserIdAndRoleId(Long userId, Long roleId) {
        UpdateWrapper<SysUserRoleEntity> wrapper = new UpdateWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("role_id", roleId);
        wrapper.eq("deleted", 0);
        wrapper.set("deleted", 1);
        wrapper.set("update_date", "NOW()");
        
        return baseDao.update(null, wrapper);
    }
    
    @Override
    public int batchInsert(List<SysUserRoleEntity> userRoles) {
        return sysUserRoleDao.batchInsert(userRoles);
    }
}
