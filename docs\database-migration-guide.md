# 数据库迁移指南 - 多租户改造

## 概述

本指南详细说明了xiaozhi项目多租户改造的数据库迁移过程，包括迁移前准备、执行步骤、验证方法和回滚方案。

## 1. 迁移前准备

### 1.1 环境检查

```bash
# 检查MySQL版本（要求5.7+）
mysql --version

# 检查数据库连接
mysql -u root -p -e "SELECT VERSION();"

# 检查当前数据库大小
mysql -u root -p xiaozhi_esp32_server -e "
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'xiaozhi_esp32_server'
GROUP BY table_schema;"
```

### 1.2 数据备份

```bash
# 创建备份目录
mkdir -p backup/$(date +%Y%m%d_%H%M%S)
cd backup/$(date +%Y%m%d_%H%M%S)

# 备份整个数据库
mysqldump -u root -p xiaozhi_esp32_server > xiaozhi_full_backup.sql

# 备份关键表
mysqldump -u root -p xiaozhi_esp32_server \
  sys_user ai_agent ai_device > xiaozhi_key_tables_backup.sql

# 验证备份文件
ls -lh *.sql
```

### 1.3 应用停止

```bash
# 停止Spring Boot应用
pkill -f "xiaozhi-manager-api"

# 或使用systemctl（如果配置了服务）
systemctl stop xiaozhi-manager-api

# 验证应用已停止
ps aux | grep xiaozhi
```

## 2. 迁移执行

### 2.1 自动迁移（推荐）

项目使用Liquibase进行数据库版本管理，启动应用时会自动执行迁移：

```bash
# 进入项目目录
cd main/manager-api

# 启动应用（会自动执行数据库迁移）
mvn spring-boot:run

# 或者只执行数据库迁移
mvn liquibase:update
```

### 2.2 手动迁移（备用方案）

如果自动迁移失败，可以手动执行SQL脚本：

```bash
# 执行主要迁移脚本
mysql -u root -p xiaozhi_esp32_server < \
  src/main/resources/db/changelog/202501151500.sql

# 执行初始数据脚本
mysql -u root -p xiaozhi_esp32_server < \
  src/main/resources/db/changelog/202501151501.sql
```

### 2.3 迁移验证

```sql
-- 检查新表是否创建成功
SHOW TABLES LIKE 'sys_organization';
SHOW TABLES LIKE 'sys_tenant';
SHOW TABLES LIKE 'ai_agent_assignment';
SHOW TABLES LIKE 'ai_agent_usage_statistics';
SHOW TABLES LIKE 'ai_device_activation';
SHOW TABLES LIKE 'ai_device_usage_statistics';

-- 检查字段是否添加成功
DESCRIBE sys_user;
DESCRIBE ai_agent;
DESCRIBE ai_device;

-- 检查数据迁移是否成功
SELECT COUNT(*) as user_count FROM sys_user WHERE tenant_id IS NOT NULL;
SELECT COUNT(*) as agent_count FROM ai_agent WHERE tenant_id IS NOT NULL;
SELECT COUNT(*) as device_count FROM ai_device WHERE tenant_id IS NOT NULL;

-- 检查默认数据是否创建
SELECT * FROM sys_organization WHERE org_code = 'DEFAULT_ORG';
SELECT * FROM sys_tenant WHERE tenant_code = 'DEFAULT_TENANT';
```

## 3. 迁移后配置

### 3.1 应用配置更新

确保application.yml中的Liquibase配置正确：

```yaml
spring:
  liquibase:
    enabled: true
    change-log: classpath:db/changelog/db.changelog-master.yaml
    contexts: dev,prod
```

### 3.2 启动应用

```bash
# 启动应用
mvn spring-boot:run

# 检查应用日志
tail -f logs/xiaozhi-manager-api.log

# 验证应用启动成功
curl http://localhost:8080/actuator/health
```

### 3.3 功能验证

```bash
# 测试用户登录
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin"}'

# 测试企业管理API
curl -X GET http://localhost:8080/sys/organization/list \
  -H "Authorization: Bearer <token>"

# 测试租户管理API
curl -X GET http://localhost:8080/sys/tenant/list \
  -H "Authorization: Bearer <token>"
```

## 4. 性能优化

### 4.1 索引优化

```sql
-- 创建复合索引以提高查询性能
ALTER TABLE sys_user ADD INDEX idx_user_tenant_type (tenant_id, user_type);
ALTER TABLE ai_agent ADD INDEX idx_agent_tenant_user (tenant_id, user_id);
ALTER TABLE ai_device ADD INDEX idx_device_tenant_user (tenant_id, user_id);
ALTER TABLE ai_agent_assignment ADD INDEX idx_assignment_tenant_status (tenant_id, status);
ALTER TABLE ai_agent_usage_statistics ADD INDEX idx_agent_stats_tenant_date (tenant_id, statistics_date);
ALTER TABLE ai_device_usage_statistics ADD INDEX idx_device_stats_tenant_date (tenant_id, statistics_date);
```

### 4.2 查询优化

```sql
-- 分析表统计信息
ANALYZE TABLE sys_user, ai_agent, ai_device, sys_organization, sys_tenant;

-- 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';
```

## 5. 监控和维护

### 5.1 数据监控

```sql
-- 租户数据量监控
SELECT 
    t.tenant_name,
    COUNT(DISTINCT u.id) as user_count,
    COUNT(DISTINCT a.id) as agent_count,
    COUNT(DISTINCT d.id) as device_count
FROM sys_tenant t
LEFT JOIN sys_user u ON t.id = u.tenant_id AND u.deleted = 0
LEFT JOIN ai_agent a ON t.id = a.tenant_id AND a.deleted = 0
LEFT JOIN ai_device d ON t.id = d.tenant_id AND d.deleted = 0
WHERE t.deleted = 0
GROUP BY t.id, t.tenant_name;

-- 配额使用情况监控
SELECT * FROM v_tenant_statistics;
```

### 5.2 定期维护

```sql
-- 清理过期的激活记录（30天前）
DELETE FROM ai_device_activation 
WHERE activation_status = 2 
  AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理旧的统计数据（1年前）
DELETE FROM ai_agent_usage_statistics 
WHERE statistics_date < DATE_SUB(CURDATE(), INTERVAL 1 YEAR);

DELETE FROM ai_device_usage_statistics 
WHERE statistics_date < DATE_SUB(CURDATE(), INTERVAL 1 YEAR);
```

## 6. 故障排除

### 6.1 常见问题

**问题1**: Liquibase迁移失败
```bash
# 解决方案：检查数据库连接和权限
mysql -u root -p -e "SHOW GRANTS;"

# 手动执行迁移
mvn liquibase:update -Dliquibase.verbose=true
```

**问题2**: 外键约束错误
```sql
-- 解决方案：检查数据完整性
SELECT * FROM sys_tenant WHERE org_id NOT IN (SELECT id FROM sys_organization);

-- 修复数据
UPDATE sys_tenant SET org_id = 1 WHERE org_id NOT IN (SELECT id FROM sys_organization);
```

**问题3**: 应用启动失败
```bash
# 解决方案：检查配置和日志
grep -i error logs/xiaozhi-manager-api.log
grep -i exception logs/xiaozhi-manager-api.log
```

### 6.2 回滚方案

如果迁移出现严重问题，可以执行回滚：

```bash
# 停止应用
systemctl stop xiaozhi-manager-api

# 恢复数据库备份
mysql -u root -p xiaozhi_esp32_server < backup/xiaozhi_full_backup.sql

# 或执行回滚脚本
mysql -u root -p xiaozhi_esp32_server < \
  src/main/resources/db/rollback/rollback-multi-tenant.sql

# 重启应用
systemctl start xiaozhi-manager-api
```

## 7. 迁移检查清单

### 7.1 迁移前检查
- [ ] 数据库备份完成
- [ ] 应用已停止
- [ ] 磁盘空间充足
- [ ] 网络连接稳定

### 7.2 迁移中检查
- [ ] SQL脚本执行成功
- [ ] 无错误日志
- [ ] 表结构正确
- [ ] 数据迁移完整

### 7.3 迁移后检查
- [ ] 应用启动成功
- [ ] API接口正常
- [ ] 用户登录正常
- [ ] 数据查询正常
- [ ] 性能指标正常

## 8. 联系支持

如果在迁移过程中遇到问题，请联系技术支持：

- **技术文档**: `docs/multi-tenant-design.md`
- **API文档**: `http://localhost:8080/doc.html`
- **日志位置**: `logs/xiaozhi-manager-api.log`

---

**重要提醒**: 
1. 生产环境迁移前请在测试环境充分验证
2. 迁移过程中请保持数据库连接稳定
3. 迁移完成后请及时验证所有功能
4. 如有问题请立即停止操作并联系技术支持
