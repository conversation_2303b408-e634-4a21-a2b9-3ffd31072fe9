<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xiaozhi.modules.sys.dao.SysTenantDao">

    <!-- 根据租户编码获取租户信息 -->
    <select id="getByTenantCode" resultType="xiaozhi.modules.sys.entity.SysTenantEntity">
        SELECT *
        FROM sys_tenant
        WHERE tenant_code = #{tenantCode}
          AND deleted = 0
    </select>

    <!-- 根据企业ID获取租户列表 -->
    <select id="getTenantsByOrgId" resultType="xiaozhi.modules.sys.entity.SysTenantEntity">
        SELECT *
        FROM sys_tenant
        WHERE org_id = #{orgId}
          AND deleted = 0
        ORDER BY create_date DESC
    </select>

    <!-- 获取即将到期的租户列表 -->
    <select id="getExpiringTenants" resultType="xiaozhi.modules.sys.entity.SysTenantEntity">
        SELECT *
        FROM sys_tenant
        WHERE expire_date IS NOT NULL
          AND expire_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY)
          AND status = 1
          AND deleted = 0
        ORDER BY expire_date ASC
    </select>

</mapper>
