-- 多租户改造回滚脚本
-- 版本: 202501151500-rollback
-- 作者: AI Assistant
-- 描述: 回滚多租户改造的数据库变更（仅在紧急情况下使用）
-- 警告: 执行此脚本将删除所有多租户相关数据，请谨慎操作！

-- =====================================================
-- 回滚前备份检查
-- =====================================================
-- 在执行回滚前，请确保已备份以下数据：
-- 1. sys_organization 表数据
-- 2. sys_tenant 表数据
-- 3. ai_agent_assignment 表数据
-- 4. ai_agent_usage_statistics 表数据
-- 5. ai_device_activation 表数据
-- 6. ai_device_usage_statistics 表数据

-- =====================================================
-- 1. 删除视图
-- =====================================================
DROP VIEW IF EXISTS `v_tenant_statistics`;

-- =====================================================
-- 2. 删除新增的统计表
-- =====================================================
DROP TABLE IF EXISTS `ai_device_usage_statistics`;
DROP TABLE IF EXISTS `ai_agent_usage_statistics`;

-- =====================================================
-- 3. 删除新增的激活表
-- =====================================================
DROP TABLE IF EXISTS `ai_device_activation`;

-- =====================================================
-- 4. 删除新增的分配表
-- =====================================================
DROP TABLE IF EXISTS `ai_agent_assignment`;

-- =====================================================
-- 5. 删除租户相关表
-- =====================================================
DROP TABLE IF EXISTS `sys_tenant`;
DROP TABLE IF EXISTS `sys_organization`;

-- =====================================================
-- 6. 回滚设备表字段
-- =====================================================
-- 删除设备表的租户相关字段
ALTER TABLE `ai_device` 
DROP COLUMN IF EXISTS `tenant_id`,
DROP COLUMN IF EXISTS `deleted`;

-- 删除设备表的索引
ALTER TABLE `ai_device` 
DROP INDEX IF EXISTS `idx_device_tenant`,
DROP INDEX IF EXISTS `idx_device_deleted`;

-- =====================================================
-- 7. 回滚智能体表字段
-- =====================================================
-- 删除智能体表的租户相关字段
ALTER TABLE `ai_agent` 
DROP COLUMN IF EXISTS `tenant_id`,
DROP COLUMN IF EXISTS `deleted`;

-- 删除智能体表的索引
ALTER TABLE `ai_agent` 
DROP INDEX IF EXISTS `idx_agent_tenant`,
DROP INDEX IF EXISTS `idx_agent_deleted`;

-- =====================================================
-- 8. 回滚用户表字段
-- =====================================================
-- 删除用户表的租户相关字段
ALTER TABLE `sys_user` 
DROP COLUMN IF EXISTS `tenant_id`,
DROP COLUMN IF EXISTS `user_type`,
DROP COLUMN IF EXISTS `real_name`,
DROP COLUMN IF EXISTS `deleted`;

-- 删除用户表的索引
ALTER TABLE `sys_user` 
DROP INDEX IF EXISTS `idx_user_tenant`,
DROP INDEX IF EXISTS `idx_user_type`,
DROP INDEX IF EXISTS `idx_user_deleted`;

-- =====================================================
-- 9. 数据完整性检查
-- =====================================================
-- 检查回滚后的数据完整性
SELECT 
    'sys_user' as table_name,
    COUNT(*) as record_count
FROM `sys_user`
UNION ALL
SELECT 
    'ai_agent' as table_name,
    COUNT(*) as record_count
FROM `ai_agent`
UNION ALL
SELECT 
    'ai_device' as table_name,
    COUNT(*) as record_count
FROM `ai_device`;

-- =====================================================
-- 10. 回滚验证
-- =====================================================
-- 验证多租户相关表是否已删除
SHOW TABLES LIKE 'sys_organization';
SHOW TABLES LIKE 'sys_tenant';
SHOW TABLES LIKE 'ai_agent_assignment';
SHOW TABLES LIKE 'ai_agent_usage_statistics';
SHOW TABLES LIKE 'ai_device_activation';
SHOW TABLES LIKE 'ai_device_usage_statistics';

-- 验证字段是否已删除
DESCRIBE `sys_user`;
DESCRIBE `ai_agent`;
DESCRIBE `ai_device`;

-- =====================================================
-- 回滚完成提示
-- =====================================================
-- 回滚完成后需要执行的操作：
-- 1. 重启应用服务
-- 2. 清理应用缓存
-- 3. 验证应用功能正常
-- 4. 通知相关人员回滚完成

SELECT 'Multi-tenant rollback completed successfully!' as status;
