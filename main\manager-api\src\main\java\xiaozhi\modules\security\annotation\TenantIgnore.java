package xiaozhi.modules.security.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 租户过滤忽略注解
 * 标记此注解的方法或类将忽略租户过滤
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TenantIgnore {
    
    /**
     * 是否忽略租户过滤
     * 
     * @return 是否忽略
     */
    boolean value() default true;
}
