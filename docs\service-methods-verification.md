# Service方法验证文档

## 1. 问题说明

用户反馈以下方法未定义：
- `SysPermissionService.count()` 方法
- `SysPermissionService.save()` 方法  
- `SysTenantService.list()` 方法
- `SysTenantService.getById()` 方法

## 2. 解决方案

已在 `BaseService` 接口中添加了这些缺失的方法：

### 2.1 BaseService接口新增方法

```java
/**
 * 插入一条记录（选择字段，策略插入）
 */
boolean save(T entity);

/**
 * 根据 ID 查询
 */
T getById(Serializable id);

/**
 * 查询所有记录
 */
List<T> list();

/**
 * 根据 Wrapper 条件，查询记录列表
 */
List<T> list(Wrapper<T> queryWrapper);

/**
 * 根据 Wrapper 条件，查询总记录数
 */
long count(Wrapper<T> queryWrapper);

/**
 * 查询总记录数
 */
long count();
```

### 2.2 BaseServiceImpl实现类新增实现

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean save(T entity) {
    return insert(entity);
}

@Override
public T getById(Serializable id) {
    return selectById(id);
}

@Override
public List<T> list() {
    return baseDao.selectList(null);
}

@Override
public List<T> list(Wrapper<T> queryWrapper) {
    return baseDao.selectList(queryWrapper);
}

@Override
public long count(Wrapper<T> queryWrapper) {
    return SqlHelper.retCount(baseDao.selectCount(queryWrapper));
}

@Override
public long count() {
    return SqlHelper.retCount(baseDao.selectCount(null));
}
```

## 3. 方法映射关系

| 新方法 | 原有方法 | 说明 |
|--------|----------|------|
| `save(T entity)` | `insert(T entity)` | 保存实体，内部调用insert |
| `getById(Serializable id)` | `selectById(Serializable id)` | 根据ID查询，内部调用selectById |
| `list()` | `baseDao.selectList(null)` | 查询所有记录 |
| `list(Wrapper<T> queryWrapper)` | `baseDao.selectList(queryWrapper)` | 条件查询 |
| `count()` | `baseDao.selectCount(null)` | 统计所有记录数 |
| `count(Wrapper<T> queryWrapper)` | `baseDao.selectCount(queryWrapper)` | 条件统计 |

## 4. 使用示例

### 4.1 SysPermissionService使用

```java
@Service
public class SomeService {
    
    @Autowired
    private SysPermissionService sysPermissionService;
    
    public void example() {
        // 保存权限
        SysPermissionEntity permission = new SysPermissionEntity();
        permission.setPermission("test:action:*");
        permission.setDescription("测试权限");
        permission.setDomain("test");
        permission.setAction("action");
        permission.setInstance("*");
        permission.setStatus(1);
        boolean saved = sysPermissionService.save(permission);
        
        // 统计权限数量
        QueryWrapper<SysPermissionEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 1);
        long count = sysPermissionService.count(wrapper);
        
        // 统计所有权限
        long totalCount = sysPermissionService.count();
    }
}
```

### 4.2 SysTenantService使用

```java
@Service
public class SomeService {
    
    @Autowired
    private SysTenantService sysTenantService;
    
    public void example() {
        // 查询所有租户
        List<SysTenantEntity> allTenants = sysTenantService.list();
        
        // 根据ID查询租户
        SysTenantEntity tenant = sysTenantService.getById(1L);
        
        // 条件查询租户
        QueryWrapper<SysTenantEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 1);
        List<SysTenantEntity> activeTenants = sysTenantService.list(wrapper);
    }
}
```

## 5. 验证方法

可以通过以下方式验证方法是否正确定义：

### 5.1 IDE验证
在IDE中打开相关Service接口，检查方法是否存在且无编译错误。

### 5.2 编译验证
```bash
cd main/manager-api
mvn compile
```

### 5.3 单元测试验证
运行提供的测试类：
```bash
mvn test -Dtest=ServiceMethodTest
```

## 6. 注意事项

1. **继承关系**：所有Service接口都继承自`BaseService<T>`，因此自动拥有这些方法
2. **实现关系**：所有ServiceImpl都继承自`BaseServiceImpl<M, T>`，因此自动实现这些方法
3. **MyBatis Plus集成**：这些方法都基于MyBatis Plus的BaseMapper实现
4. **事务支持**：save方法添加了`@Transactional`注解，支持事务回滚

## 7. 总结

通过扩展BaseService接口和BaseServiceImpl实现类，已经解决了缺失方法的问题。所有继承自BaseService的Service接口现在都拥有了完整的CRUD方法，包括：

- ✅ `save(T entity)` - 保存实体
- ✅ `getById(Serializable id)` - 根据ID查询
- ✅ `list()` - 查询所有记录  
- ✅ `list(Wrapper<T> queryWrapper)` - 条件查询
- ✅ `count()` - 统计所有记录
- ✅ `count(Wrapper<T> queryWrapper)` - 条件统计

这些方法与MyBatis Plus的命名约定保持一致，便于开发者使用。
