package xiaozhi.modules.sys.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import xiaozhi.common.entity.BaseEntity;

/**
 * 企业组织实体
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_organization")
public class SysOrganizationEntity extends BaseEntity {
    
    /**
     * 企业编码
     */
    private String orgCode;
    
    /**
     * 企业名称
     */
    private String orgName;
    
    /**
     * 企业类型：1-普通企业，2-代理商
     */
    private Integer orgType;
    
    /**
     * 联系人
     */
    private String contactPerson;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 联系邮箱
     */
    private String contactEmail;
    
    /**
     * 企业地址
     */
    private String address;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 到期时间
     */
    private Date expireDate;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}
